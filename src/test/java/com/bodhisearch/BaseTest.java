package com.bodhisearch;

import static com.bodhisearch.BodhiResourceProvider.GROUP_ADMINS;
import static com.bodhisearch.BodhiResourceProvider.GROUP_MANAGERS;
import static com.bodhisearch.BodhiResourceProvider.GROUP_POWER_USERS;
import static com.bodhisearch.BodhiResourceProvider.GROUP_USERS;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_ADMIN;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_MANAGER;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_POWER_USER;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_USER;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.junit.jupiter.api.BeforeAll;
import org.keycloak.TokenVerifier;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.common.VerificationException;
import org.keycloak.jose.jws.JWSInput;
import org.keycloak.jose.jws.JWSInputException;
import org.keycloak.representations.AccessToken;
import org.keycloak.representations.idm.KeysMetadataRepresentation.KeyMetadataRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import com.bodhisearch.templates.RealmConfigGenerator;
import com.bodhisearch.util.BodhiProviderClient;
import com.bodhisearch.util.ClientPair;
import com.bodhisearch.util.KeycloakAdminClient;
import com.bodhisearch.util.KeycloakTestUtils;
import com.bodhisearch.util.TokenPair;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import dasniko.testcontainers.keycloak.KeycloakContainer;
import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;

@Testcontainers
public class BaseTest {
  public static final String REALM = "bodhi";
  public static final String PROVIDER_ID = "bodhi";
  public static final Logger LOGGER = LoggerFactory.getLogger(BaseTest.class);
  protected static String tokenUrl;
  protected static String registerClientUrl;
  protected static Keycloak admin;
  protected static RealmResource realm;
  protected static String resourceAdminUrl;
  protected static String addToGroupUrl;
  protected static String hasAdminUrl;

  // Client instances
  protected static BodhiProviderClient bodhiProviderClient;
  protected static KeycloakAdminClient keycloakAdminClient;

  @Container
  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:23.0.7")
      .withFeaturesEnabled("admin-fine-grained-authz", "token-exchange")
      .withProviderClassesFrom("target/classes")
      .withEnv("APP_ENV", "test")
  // .withDebugFixedPort(8787, true)
  ;

  @BeforeAll
  public static void importConfigs() {
    String filename = "./src/test/resources/import-files/bodhi-realm-generated.json";
    RealmConfigGenerator.generate(filename);
    KeycloakTestUtils.importFile(keycloak.getAuthServerUrl(), keycloak.getAdminUsername(),
        keycloak.getAdminPassword(),
        filename);
    tokenUrl = String.format("%s/realms/%s/protocol/openid-connect/token", keycloak.getAuthServerUrl(), REALM);
    registerClientUrl = String.format("%s/realms/%s/bodhi/clients", keycloak.getAuthServerUrl(), REALM);
    admin = keycloak.getKeycloakAdminClient();
    realm = admin.realm(REALM);

    resourceAdminUrl = buildResourceAdminUrl(REALM, PROVIDER_ID);
    addToGroupUrl = buildAddToGroupUrl(REALM, PROVIDER_ID);
    hasAdminUrl = buildHasAdminUrl(REALM, PROVIDER_ID);

    // Initialize client instances
    bodhiProviderClient = new BodhiProviderClient(keycloak.getAuthServerUrl(), REALM, PROVIDER_ID);
    keycloakAdminClient = new KeycloakAdminClient(keycloak.getAuthServerUrl(), REALM, PROVIDER_ID, admin);
  }

  public static RequestSpecification given() {
    return KeycloakTestUtils.given();
  }

  public TokenPair refreshTokenFlow(ClientPair clientPair, String refreshToken) {
    return keycloakAdminClient.refreshTokenFlow(clientPair, refreshToken);
  }

  public static Response getUserTokenResponseWith(ClientPair clientPair, String username,
      String password, List<String> scopes) {
    return keycloakAdminClient.getUserTokenResponseWith(clientPair, username, password, scopes);
  }

  public static TokenPair getUserTokenWith(ClientPair clientPair, String username,
      String password, List<String> scopes) {
    return keycloakAdminClient.getUserTokenWith(clientPair, username, password, scopes);
  }

  // TODO: remove method, return TokenPair
  public static String getUserTokenWith(ClientPair clientPair, String username, String password) {
    return keycloakAdminClient.getUserTokenWith(clientPair, username, password);
  }

  public static TokenPair getUserTokenPairWith(ClientPair clientPair, String username,
      String password, List<String> scopes) {
    return getUserTokenWith(clientPair, username, password, scopes);
  }

  protected static String getTokenForClient(ClientPair clientPair) {
    return keycloakAdminClient.getTokenForClient(clientPair);
  }

  protected String[] exchangeToOfflineToken(ClientPair clientPair, String subjectToken,
      List<String> scopes) {
    JsonPath response = given()
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "urn:ietf:params:oauth:grant-type:token-exchange")
        .formParam("client_id", clientPair.clientId)
        .formParam("client_secret", clientPair.clientSecret)
        .formParam("subject_token", subjectToken)
        .formParam("requested_token_type", "urn:ietf:params:oauth:token-type:refresh_token")
        .formParam("scope", scopes.stream().collect(Collectors.joining(" ")))
        .post(tokenUrl)
        .then()
        .statusCode(200)
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .body("access_token", notNullValue())
        .extract()
        .jsonPath();
    return new String[] { response.getString("access_token"), response.getString("refresh_token") };
  }

  protected String exchangeToken(String clientId, String sourceToken, String audience, String accessToken) {
    return exchangeTokenResponse(clientId, sourceToken, audience, accessToken)
        .then()
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .statusCode(200)
        .body("access_token", notNullValue())
        .extract().jsonPath()
        .getString("access_token");
  }

  protected Response exchangeTokenResponse(String clientId, String userClientAccessToken, String resource,
      String resourceTokenAsBearer) {
    RequestSpecification request = given()
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "urn:ietf:params:oauth:grant-type:token-exchange")
        .formParam("subject_token", userClientAccessToken)
        .formParam("client_id", clientId)
        .formParam("audience", resource)
        .formParam("scope", "openid email profile roles");
    if (resourceTokenAsBearer != null) {
      request.header("Authorization", resourceTokenAsBearer);
    }
    return request
        .when()
        .post(tokenUrl);
  }

  protected JWSInput getJws(String accessToken) {
    try {
      return new JWSInput(accessToken);
    } catch (JWSInputException e) {
      throw new RuntimeException(e);
    }
  }

  protected AccessToken decodeToken(String token) {
    try {
      TokenVerifier<AccessToken> verifier = TokenVerifier.create(token, AccessToken.class);
      return verifier.parse().getToken();
    } catch (VerificationException e) {
      throw new RuntimeException("Failed to decode token", e);
    }
  }

  protected KeyMetadataRepresentation getKeyRepr(String userToken)
      throws JsonProcessingException, JsonMappingException {
    String header = userToken.split("\\.")[0];
    byte[] decodedBytes = Base64.getDecoder().decode(header);
    String decodedHeader = new String(decodedBytes, StandardCharsets.UTF_8);
    ObjectMapper objectMapper = new ObjectMapper();
    JsonNode jsonHeader = objectMapper.readTree(decodedHeader);
    String kid = jsonHeader.get("kid").asText();
    KeyMetadataRepresentation keyRepr = realm.keys().getKeyMetadata().getKeys().stream()
        .filter(k -> k.getKid().equals(kid))
        .findFirst().get();
    return keyRepr;
  }

  protected JsonPath registerClient() {
    return bodhiProviderClient.registerClient("http://bodhiapp.localhost/app/callback");
  }

  // TokenPair class for holding access and refresh tokens

  protected ClientPair registerClientAndReturnClientPair() {
    return bodhiProviderClient.registerClientAndReturnClientPair();
  }

  protected ClientPair createPublicClientForUser() {
    long timestamp = System.currentTimeMillis();
    String clientId = "test-client-" + timestamp;

    org.keycloak.representations.idm.ClientRepresentation client = new org.keycloak.representations.idm.ClientRepresentation();
    client.setClientId(clientId);
    client.setEnabled(true);
    client.setClientAuthenticatorType("client-secret");
    client.setSecret("change-me");
    client.setRedirectUris(Arrays.asList("*"));
    client.setWebOrigins(Arrays.asList("*"));
    client.setBearerOnly(false);
    client.setConsentRequired(false);
    client.setStandardFlowEnabled(true);
    client.setImplicitFlowEnabled(false);
    client.setDirectAccessGrantsEnabled(true);
    client.setServiceAccountsEnabled(false); // Key difference from resource clients
    client.setPublicClient(true); // Key difference from resource clients
    client.setProtocol("openid-connect");
    client.setFullScopeAllowed(false);

    // Create the client
    jakarta.ws.rs.core.Response response = realm.clients().create(client);
    assertThat("Failed to create public client: " + response.getStatus() + " " + response.getStatusInfo(),
        response.getStatus(), is(201));
    return new ClientPair(clientId, "change-me");
  }

  protected Response makeResourceAdmin(String token, String userToMakeFirstAdmin) {
    return bodhiProviderClient.makeResourceAdmin(token, userToMakeFirstAdmin);
  }

  protected static String buildResourceAdminUrl(String realm, String providerId) {
    return String.format(
        "%s/realms/%s/%s/clients/make-resource-admin",
        keycloak.getAuthServerUrl(),
        realm,
        providerId);
  }

  protected static String buildAddToGroupUrl(String realm, String providerId) {
    return String.format(
        "%s/realms/%s/%s/clients/add-user-to-group",
        keycloak.getAuthServerUrl(),
        realm,
        providerId);
  }

  protected static String buildHasAdminUrl(String realm, String providerId) {
    return String.format(
        "%s/realms/%s/%s/clients/has-resource-admin",
        keycloak.getAuthServerUrl(),
        realm,
        providerId);
  }

  // Method for using the BodhiResourceProvider endpoint to add users to groups
  protected Response addUserToGroupViaAPI(String token, String testUser, String group) {
    return bodhiProviderClient.addUserToGroup(token, testUser, group);
  }

  // TestUser class for simplified user creation
  public static class TestUser {
    public final String userId;
    public final String firstName;
    public final String lastName;
    public final String email;
    public final String password;

    public TestUser(String userId, String firstName, String lastName, String email, String password) {
      this.userId = userId;
      this.firstName = firstName;
      this.lastName = lastName;
      this.email = email;
      this.password = password;
    }
  }

  // Dynamic user creation utilities
  protected TestUser createUser() {
    long timestamp = System.currentTimeMillis();
    String email = "test-user-" + timestamp + "@email.com";
    String password = "test-password-" + timestamp;
    String firstName = "Test";
    String lastName = "User";

    String userId = createUser(email, firstName, lastName, password);
    return new TestUser(userId, firstName, lastName, email, password);
  }

  @SuppressWarnings("UseSpecificCatch")
  protected String createUser(String email, String firstName, String lastName, String password) {
    try {
      org.keycloak.representations.idm.UserRepresentation user = new org.keycloak.representations.idm.UserRepresentation();
      user.setEmail(email);
      user.setUsername(email);
      user.setFirstName(firstName);
      user.setLastName(lastName);
      user.setEnabled(true);
      user.setEmailVerified(true);

      // Create the user
      jakarta.ws.rs.core.Response response = realm.users().create(user);
      if (response.getStatus() != 201) {
        throw new RuntimeException("Failed to create user: " + response.getStatus() + " " + response.getStatusInfo());
      }

      // Extract user ID from location header
      String location = response.getHeaderString("Location");
      String userId = location.substring(location.lastIndexOf('/') + 1);

      // Set password
      org.keycloak.representations.idm.CredentialRepresentation credential = new org.keycloak.representations.idm.CredentialRepresentation();
      credential.setType(org.keycloak.representations.idm.CredentialRepresentation.PASSWORD);
      credential.setValue(password);
      credential.setTemporary(false);

      realm.users().get(userId).resetPassword(credential);

      return userId;
    } catch (Exception e) {
      throw new RuntimeException("Failed to create user: " + email, e);
    }
  }

  protected void deleteUser(String userId) {
    try {
      realm.users().get(userId).remove();
    } catch (Exception e) {
      LOGGER.warn("Failed to delete user: " + userId, e);
    }
  }

  protected void addUserToClientGroup(String userId, String clientId, String groupName) {
    // Find the top-level group for this client (users-{client_id})
    String topLevelGroupName = String.format("users-%s", clientId);
    Optional<org.keycloak.representations.idm.GroupRepresentation> topLevelGroupOpt = realm.groups().groups().stream()
        .filter(g -> g.getName().equals(topLevelGroupName))
        .findFirst();

    assertThat("Top-level group should exist for client: " + topLevelGroupName,
        topLevelGroupOpt.isPresent(), is(true));

    // Find the sub-group within the top-level group
    Optional<org.keycloak.representations.idm.GroupRepresentation> subGroupOpt = realm.groups()
        .group(topLevelGroupOpt.get().getId()).getSubGroups(0, -1, false).stream()
        .filter(g -> g.getName().equals(groupName))
        .findFirst();

    assertThat("Sub-group should exist: " + groupName + " in " + topLevelGroupName,
        subGroupOpt.isPresent(), is(true));

    // Add user to the sub-group
    realm.users().get(userId).joinGroup(subGroupOpt.get().getId());

    LOGGER.info("Added user {} to client {} group {}", userId, clientId, groupName);
  }

  // New utility methods for role and group management (using group-based
  // approach)
  protected void assignRoleToUser(String userId, String clientId, String roleName) {
    // Convert role name to appropriate group name
    String groupName = roleToGroupName(roleName);
    assertThat("Role should map to a valid group name: " + roleName, groupName, notNullValue());

    addUserToClientGroup(userId, clientId, groupName);
    LOGGER.info("Assigned role {} to user {} for client {} via groups", roleName, userId, clientId);
  }

  private String roleToGroupName(String roleName) {
    // Convert role names to group names based on the system's mapping
    switch (roleName) {
      case RESOURCE_ADMIN:
        return GROUP_ADMINS;
      case RESOURCE_MANAGER:
        return GROUP_MANAGERS;
      case RESOURCE_POWER_USER:
        return GROUP_POWER_USERS;
      case RESOURCE_USER:
        return GROUP_USERS;
      default:
        LOGGER.warn("Unknown role name: {}", roleName);
        return null;
    }
  }

  // Method for using the BodhiResourceProvider endpoint to add users to groups
  // (4-parameter version)
  protected Response addUserToGroup(String url, String token, String username, String group) {
    return given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"%s\", \"add\": true}", username, group))
        .when()
        .post(url);
  }

  protected TestUser createUserWithRole(String clientId, String roleName) {
    TestUser user = createUser();
    assignRoleToUser(user.userId, clientId, roleName);
    return user;
  }

  protected TestUser createUserInGroup(String clientId, String groupName) {
    TestUser user = createUser();
    addUserToClientGroup(user.userId, clientId, groupName);
    return user;
  }

  // Specialized client creation methods
  protected ClientPair createClientWithoutTokenExchangePermission() {
    long timestamp = System.currentTimeMillis();
    String clientId = "test-no-exchange-" + timestamp;

    org.keycloak.representations.idm.ClientRepresentation client = new org.keycloak.representations.idm.ClientRepresentation();
    client.setClientId(clientId);
    client.setEnabled(true);
    client.setClientAuthenticatorType("client-secret");
    client.setSecret("change-me");
    client.setRedirectUris(Arrays.asList("http://localhost/callback"));
    client.setWebOrigins(Arrays.asList("+"));
    client.setBearerOnly(false);
    client.setConsentRequired(false);
    client.setStandardFlowEnabled(true);
    client.setImplicitFlowEnabled(false);
    client.setDirectAccessGrantsEnabled(true);
    client.setServiceAccountsEnabled(true);
    client.setPublicClient(false);
    client.setProtocol("openid-connect");
    client.setFullScopeAllowed(false);

    // Create the client
    jakarta.ws.rs.core.Response response = realm.clients().create(client);
    assertThat(
        "Failed to create client without token exchange: " + response.getStatus() + " " + response.getStatusInfo(),
        response.getStatus(), is(201));

    return new ClientPair(clientId, "change-me");
  }
}
