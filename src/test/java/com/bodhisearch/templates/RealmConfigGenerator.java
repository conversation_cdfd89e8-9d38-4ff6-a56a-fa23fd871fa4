package com.bodhisearch.templates;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;

public class RealmConfigGenerator {
  public static final List<String> CLIENT_ROLES = Arrays.asList("resource_user", "resource_power_user",
      "resource_manager", "resource_admin");
  public static final List<Group> GROUPS = Arrays.asList(
      new Group("users", Arrays.asList("resource_user")),
      new Group("power-users", Arrays.asList("resource_user", "resource_power_user")),
      new Group("managers", Arrays.asList("resource_user", "resource_power_user", "resource_manager")),
      new Group("admins", Arrays.asList("resource_user", "resource_power_user", "resource_manager", "resource_admin")));

  public static void main(String[] args) {
    generate("./src/test/resources/import-files/bodhi-realm-generated.json");
  }

  public static void generate(String filename) {
    List<User> users = Arrays.asList(
        new User("<EMAIL>", "Seed", "User", Arrays.asList("/users-resource-seed/users")));

    Map<String, Object> dataModel = new HashMap<>();
    dataModel.put("resources", Arrays.asList("resource-seed"));
    dataModel.put("clients", Arrays.asList());
    dataModel.put("users", users);
    dataModel.put("clientRoles", CLIENT_ROLES);
    dataModel.put("userGroups", GROUPS);
    dataModel.put("skipTokenExchange", Arrays.asList());
    dataModel.put("skipGroup", Arrays.asList());

    generateWithDataModel(filename, dataModel);
  }

  public static void generateForIntegrationTests(String filename) {
    // For integration tests, we don't pre-create users - they will be created during the test
    List<User> users = Arrays.asList(); // Empty list

    Map<String, Object> dataModel = new HashMap<>();
    dataModel.put("resources", Arrays.asList("resource-seed"));
    dataModel.put("clients", Arrays.asList());
    dataModel.put("users", users);
    dataModel.put("clientRoles", CLIENT_ROLES);
    dataModel.put("userGroups", GROUPS);
    dataModel.put("skipTokenExchange", Arrays.asList());
    dataModel.put("skipGroup", Arrays.asList());

    generateWithDataModel(filename, dataModel);
  }

  private static void generateWithDataModel(String filename, Map<String, Object> dataModel) {
    Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);
    cfg.setClassForTemplateLoading(RealmConfigGenerator.class, "/");

    try {
      Template template = cfg.getTemplate("/import-files/bodhi-realm-setup.ftl");
      try (Writer out = new FileWriter(filename)) {
        template.process(dataModel, out);
      }
    } catch (IOException | TemplateException e) {
      e.printStackTrace();
    }
  }
}
