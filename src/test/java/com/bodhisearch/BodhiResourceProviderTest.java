package com.bodhisearch;

import static com.bodhisearch.BodhiResourceProvider.RESOURCE_ADMIN;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_MANAGER;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_POWER_USER;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_USER;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.endsWith;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.hamcrest.Matchers.startsWith;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.keycloak.admin.client.resource.GroupResource;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.RoleScopeResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.ClientRepresentation;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.bodhisearch.util.ClientPair;
import com.bodhisearch.util.TokenPair;

import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;

/**
 * Test class for BodhiResourceProvider functionality.
 *
 * This class tests all endpoints and methods provided by BodhiResourceProvider:
 * - Client registration and creation (newResource endpoint)
 * - Admin management (makeFirstResourceAdmin endpoint)
 * - User group management (addUserToGroup endpoint)
 * - Client permission validation (hasResourceAdmin endpoint)
 * - Client permission restrictions and security
 * - Error handling and edge cases
 */
public class BodhiResourceProviderTest extends BaseTest {

  // ========================================
  // CLIENT REGISTRATION TESTS
  // ========================================

  @Test
  public void testRegisterResourceCreatesClientWithCorrectConfiguration() {
    JsonPath jsonPath = registerClient();
    String clientId = jsonPath.getString("client_id");
    String clientSecret = jsonPath.getString("client_secret");
    String publicKey = jsonPath.getString("public_key");
    String issuer = jsonPath.getString("issuer");
    String kid = jsonPath.getString("kid");
    String alg = jsonPath.getString("alg");

    // Verify client configuration
    ClientRepresentation fetchedClient = realm.clients().findByClientId(clientId).get(0);
    assertThat(fetchedClient.isEnabled(), is(true));
    assertThat(fetchedClient.getRedirectUris(), containsInAnyOrder("http://bodhiapp.localhost/app/callback"));
    assertThat(fetchedClient.getWebOrigins(), containsInAnyOrder("+"));
    assertThat(fetchedClient.isConsentRequired(), is(false));
    assertThat(fetchedClient.isStandardFlowEnabled(), is(true));
    assertThat(fetchedClient.isDirectAccessGrantsEnabled(), is(true));
    assertThat(fetchedClient.isServiceAccountsEnabled(), is(true));
    assertThat(fetchedClient.isPublicClient(), is(false));
    assertThat(fetchedClient.isFullScopeAllowed(), is(false));

    // Verify group structure and roles
    Optional<GroupRepresentation> group = realm.groups().groups().stream()
        .filter(g -> g.getPath().equals(String.format("/users-%s", clientId))).findFirst();
    assertThat(group.isPresent(), is(true));
    assertThat(group.get().getSubGroupCount(), is(4l));

    GroupResource topLevelGroupResource = realm.groups().group(group.get().getId());
    Map<String, Map<String, Set<String>>> groupRoleMap = topLevelGroupResource.getSubGroups(0, -1, false).stream()
        .collect(Collectors.toConcurrentMap(g -> {
          return ((GroupRepresentation) g).getPath();
        }, g -> {
          return g.getClientRoles().keySet().stream()
              .collect(Collectors.toMap(k -> k, v -> new HashSet<>(g.getClientRoles().get(v))));
        }));

    String topLevelGroup = String.format("/users-%s", clientId);
    String usersGroup = String.format("%s/users", topLevelGroup);
    String powerUsersGroup = String.format("%s/power-users", topLevelGroup);
    String managersGroup = String.format("%s/managers", topLevelGroup);
    String adminsGroup = String.format("%s/admins", topLevelGroup);

    Map<String, Map<String, Set<String>>> expected = new HashMap<String, Map<String, Set<String>>>() {
      {
        put(usersGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId, new HashSet<String>(Arrays.asList("resource_user")));
          }
        });
        put(powerUsersGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId, new HashSet<String>(Arrays.asList("resource_user", "resource_power_user")));
          }
        });
        put(managersGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId,
                new HashSet<String>(Arrays.asList("resource_user", "resource_power_user", "resource_manager")));
          }
        });
        put(adminsGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId, new HashSet<String>(
                Arrays.asList("resource_user", "resource_power_user", "resource_manager", "resource_admin")));
          }
        });
      }
    };
    assertThat(groupRoleMap, equalTo(expected));

    // Verify token generation works
    String resourceToken = getTokenForClient(new ClientPair(clientId, clientSecret));
    assertThat(resourceToken, notNullValue());

    // Verify response metadata
    assertThat(publicKey, notNullValue());
    assertThat(issuer, startsWith("http://localhost"));
    assertThat(issuer, endsWith("/realms/bodhi"));
    assertThat(kid, notNullValue());
    assertThat(alg, equalTo("RS256"));
  }

  @Test
  public void testClientCreationWithTestPrefix() {
    // Test that client creation works with test prefix parameter when
    // live_test=true
    JsonPath jsonPath = given()
        .contentType(ContentType.JSON)
        .body("{\"redirect_uris\": [\"http://bodhiapp.localhost/app/callback\"]}")
        .when()
        .post(String.format("%s?live_test=true", registerClientUrl))
        .then()
        .body("error", nullValue())
        .statusCode(201)
        .body("client_id", notNullValue())
        .body("client_secret", notNullValue())
        .extract()
        .jsonPath();

    String clientId = jsonPath.getString("client_id");
    assertNotNull(clientId, "Client ID should not be null");
    assertTrue(clientId.startsWith("test-resource-"), "Client ID should have test prefix when live_test=true");
  }

  // ========================================
  // CLIENT PERMISSION TESTS
  // ========================================

  @Test
  public void testResourceClientCannotListOtherClients() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    String resourceToken = getTokenForClient(clientPair);

    given()
        .header("Authorization", "Bearer " + resourceToken)
        .when()
        .get(String.format("%s/admin/realms/%s/clients", keycloak.getAuthServerUrl(), REALM))
        .then()
        .statusCode(403);
  }

  @Test
  public void testResourceClientCannotViewItself() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    String resourceToken = getTokenForClient(clientPair);

    given()
        .header("Authorization", "Bearer " + resourceToken)
        .when()
        .get(String.format("%s/admin/realms/bodhi/clients/%s", keycloak.getAuthServerUrl(), clientPair.clientId))
        .then()
        .statusCode(403);
  }

  @Test
  public void testResourceClientCannotUpdateItself() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    String resourceToken = getTokenForClient(clientPair);

    given()
        .header("Authorization", "Bearer " + resourceToken)
        .contentType(ContentType.JSON)
        .body("{\"name\": \"Updated Name\"}")
        .when()
        .put(String.format("%s/admin/realms/bodhi/clients/%s", keycloak.getAuthServerUrl(), clientPair.clientId))
        .then()
        .statusCode(403);
  }

  @Test
  public void testResourceClientCannotConfigureScopes() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    String resourceToken = getTokenForClient(clientPair);
    String scopeId = realm.clientScopes().findAll().stream().filter(scope -> scope.getName().equals("phone"))
        .findFirst().get().getId();

    given()
        .header("Authorization", "Bearer " + resourceToken)
        .when()
        .put(String.format("%s/admin/realms/bodhi/clients/%s/default-client-scopes/%s", keycloak.getAuthServerUrl(),
            clientPair.clientId, scopeId))
        .then()
        .statusCode(403);
  }

  // ========================================
  // ADMIN MANAGEMENT TESTS
  // ========================================

  @Test
  public void testMakeResourceAdminAssignsAllRoles() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    TestUser adminUser = createUser();

    String clientToken = getTokenForClient(clientPair);
    makeResourceAdmin(clientToken, adminUser.email)
        .then()
        .statusCode(201);

    TokenPair userTokenPair = getUserTokenPairWith(clientPair, adminUser.email, adminUser.password,
        Arrays.asList("openid", "email", "profile", "roles"));
    DecodedJWT jwt = JWT.decode(userTokenPair.access);
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split("\\s+"));
    assertEquals(new HashSet<>(Arrays.asList("openid", "email", "profile", "roles")), new HashSet<>(claimScopes));
    assertEquals("RS256", jwt.getAlgorithm());
    String keyId = jwt.getKeyId();

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> resourceRoles = (List) ((Map) jwt.getClaim("resource_access").asMap().get(clientPair.clientId))
        .get("roles");
    List<String> allRoles = Arrays.asList(RESOURCE_USER, RESOURCE_POWER_USER, RESOURCE_MANAGER, RESOURCE_ADMIN);
    assertThat(allRoles, containsInAnyOrder(resourceRoles.toArray()));

    // Test refresh token maintains roles
    TokenPair newTokenPair = refreshTokenFlow(clientPair, userTokenPair.refresh);
    DecodedJWT newJwt = JWT.decode(newTokenPair.access);
    String newClaimScope = newJwt.getClaim("scope").asString();
    List<String> newClaimScopes = Arrays.asList(newClaimScope.split(" "));
    assertEquals(new HashSet<>(Arrays.asList("openid", "email", "profile", "roles")), new HashSet<>(newClaimScopes));
    assertEquals(keyId, newJwt.getKeyId());
    assertEquals("RS256", newJwt.getAlgorithm());

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> newResourceRoles = (List) ((Map) newJwt.getClaim("resource_access").asMap().get(clientPair.clientId))
        .get("roles");
    assertThat(allRoles, containsInAnyOrder(newResourceRoles.toArray()));
  }

  // ========================================
  // USER GROUP MANAGEMENT TESTS
  // ========================================

  @Test
  public void testAddUserToGroupGrantsUserRole() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    TestUser adminUser = createUser();
    TestUser regularUser = createUser();

    String clientToken = getTokenForClient(clientPair);
    makeResourceAdmin(clientToken, adminUser.email)
        .then()
        .statusCode(201);

    String adminAccessToken = getUserTokenWith(clientPair, adminUser.email, adminUser.password);
    addUserToGroup(addToGroupUrl, adminAccessToken, regularUser.email, "users").then()
        .body("error", nullValue())
        .statusCode(201);

    List<String> scopes = Arrays.asList("openid", "email", "profile", "roles");
    TokenPair userTokenPair = getUserTokenPairWith(clientPair, regularUser.email, regularUser.password, scopes);
    DecodedJWT jwt = JWT.decode(userTokenPair.access);
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split(" "));
    assertEquals(new HashSet<>(scopes), new HashSet<>(claimScopes));

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> resourceRoles = (List) ((Map) jwt.getClaim("resource_access").asMap().get(clientPair.clientId))
        .get("roles");
    List<String> userRoles = Arrays.asList(RESOURCE_USER);
    assertThat(userRoles, containsInAnyOrder(resourceRoles.toArray()));
    String keyId = jwt.getKeyId();
    assertEquals("RS256", jwt.getAlgorithm());

    // Test refresh token maintains roles
    TokenPair newTokenPair = refreshTokenFlow(clientPair, userTokenPair.refresh);
    DecodedJWT newJwt = JWT.decode(newTokenPair.access);
    String newClaimScope = newJwt.getClaim("scope").asString();
    List<String> newClaimScopes = Arrays.asList(newClaimScope.split(" "));
    assertEquals(new HashSet<>(scopes), new HashSet<>(newClaimScopes));
    assertEquals(keyId, newJwt.getKeyId());
    assertEquals("RS256", newJwt.getAlgorithm());

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> newResourceRoles = (List) ((Map) newJwt.getClaim("resource_access").asMap().get(clientPair.clientId))
        .get("roles");
    assertThat(userRoles, containsInAnyOrder(newResourceRoles.toArray()));
  }

  // ========================================
  // EXISTING TESTS (refactored without try-finally)
  // ========================================

  @Test
  public void testMakeFirstAdmin() {
    ClientPair makeFirstAdminClient = registerClientAndReturnClientPair();
    TestUser makeFirstAdminUser = createUser();

    String token = getTokenForClient(makeFirstAdminClient);
    makeResourceAdmin(token, makeFirstAdminUser.email)
        .then()
        .statusCode(201);

    ClientRepresentation client = realm.clients().findByClientId(makeFirstAdminClient.clientId).get(0);
    UserRepresentation user = realm.users().search(makeFirstAdminUser.email).get(0);
    assertNotNull(user);
    UserResource userResource = realm.users().get(user.getId());
    RoleScopeResource clientRoles = userResource.roles().clientLevel(client.getId());
    RoleRepresentation adminRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(RESOURCE_ADMIN)).collect(Collectors.toList()).get(0);
    assertNotNull(adminRole);
    RoleRepresentation managerRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(RESOURCE_MANAGER)).collect(Collectors.toList()).get(0);
    assertNotNull(managerRole);
    RoleRepresentation userRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(RESOURCE_USER)).collect(Collectors.toList()).get(0);
    assertNotNull(userRole);
    List<GroupRepresentation> userGroups = userResource.groups(null, null).stream().collect(Collectors.toList());
    Optional<GroupRepresentation> adminGroup = userGroups.stream().filter(g -> g.getName().equals("admins"))
        .findFirst();
    assertTrue(adminGroup.isPresent());
    String adminGroupPath = String.format("/users-%s/admins", makeFirstAdminClient.clientId);
    assertEquals(adminGroupPath, adminGroup.get().getPath());
    assertEquals(1, userGroups.size());

    // Verify the admin user is a member of the admin group
    GroupResource adminGroupResource = realm.groups().group(adminGroup.get().getId());
    List<UserRepresentation> adminGroupMembers = adminGroupResource.members();
    Optional<UserRepresentation> adminUserInGroup = adminGroupMembers.stream()
        .filter(u -> u.getEmail().equals(makeFirstAdminUser.email))
        .findFirst();
    assertThat("Admin user should be a member of the admin group", adminUserInGroup.isPresent(), is(true));
  }

  @Test
  public void testMakeFirstAdminUnauthorized() {
    TestUser someUser = createUser();
    given()
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", someUser.email))
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("invalid session"))
        .statusCode(401);
  }

  @Test
  public void testMakeFirstAdminErrorIfNotServiceToken() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getUserTokenWith(mainResourceClient, mainAdminUser.email, mainAdminUser.password);
    TestUser testUser = createUser();
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", testUser.email))
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("not a service account token"))
        .statusCode(401);
  }

  @Test
  public void testMakeFirstAdminInvalidToken() {
    ClientPair emptyResourceClient = registerClientAndReturnClientPair();
    String token = getTokenForClient(emptyResourceClient) + "foobar";
    TestUser testUser = createUser();
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", testUser.email))
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("invalid session"))
        .statusCode(401);
  }

  @Test
  public void testMakeFirstAdminFailsIfAlreadyHaveAdmin() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getTokenForClient(mainResourceClient);
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", mainAdminUser.email))
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("resource already has a admin user"))
        .statusCode(400);
  }

  @Test
  public void testMakeFirstAdminFailsIfTopLevelGroupNotConfigured() {
    ClientPair missingGroupClient = createClientWithoutGroups();
    TestUser mainAdminUser = createUser();

    String token = getTokenForClient(missingGroupClient);
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", mainAdminUser.email))
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("resource group not configured for client"))
        .statusCode(500);
  }

  @Test
  public void testMakeFirstAdminUserNotFound() {
    ClientPair emptyResourceClient = registerClientAndReturnClientPair();

    String token = getTokenForClient(emptyResourceClient);
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body("{\"username\": \"<EMAIL>\"}")
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("user not found"))
        .statusCode(400);
  }

  @Test
  public void testAddUserToGroupByResourceAdmin() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    TestUser testApproveUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getUserTokenWith(mainResourceClient, mainAdminUser.email, mainAdminUser.password);
    addUserToGroup(addToGroupUrl, token, testApproveUser.email, "users")
        .then()
        .body("message", equalTo("added to group"))
        .statusCode(201);

    ClientRepresentation client = realm.clients().findByClientId(mainResourceClient.clientId).get(0);
    UserRepresentation user = realm.users().search(testApproveUser.email).get(0);
    RoleScopeResource clientRoles = realm.users().get(user.getId()).roles().clientLevel(client.getId());
    RoleRepresentation clientRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(RESOURCE_USER)).collect(Collectors.toList()).get(0);
    assertNotNull(clientRole);
    UserResource userResource = realm.users().get(user.getId());
    List<GroupRepresentation> userGroups = userResource.groups(null, null).stream().collect(Collectors.toList());
    assertEquals(String.format("/users-%s/users", mainResourceClient.clientId), userGroups.get(0).getPath());
    assertEquals(1, userGroups.size());
  }

  @Test
  public void testAddUserToGroupAlreadyMember() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getUserTokenWith(mainResourceClient, mainAdminUser.email, mainAdminUser.password);
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"admins\", \"add\": true}", mainAdminUser.email))
        .when()
        .post(addToGroupUrl)
        .then()
        .body("message", equalTo("user already member of group"))
        .statusCode(200);
  }

  @Test
  public void testAddUserToGroupUnauthorized() {
    TestUser testUser = createUser();
    given()
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"users\", \"add\": true}", testUser.email))
        .when()
        .post(addToGroupUrl)
        .then()
        .body("error", equalTo("invalid session"))
        .statusCode(401);
  }

  @Test
  public void testAddUserToGroupByOtherClientAdmin() {
    ClientPair otherResourceClient = registerClientAndReturnClientPair();
    TestUser otherAdminUser = createUser();
    TestUser userToAddByOtherAdmin = createUser();
    setupAdminUser(otherResourceClient, otherAdminUser);

    TestUser otherUser = createUser();
    TokenPair otherAdminToken = getUserTokenWith(otherResourceClient, otherAdminUser.email, otherAdminUser.password,
        Arrays.asList("openid", "email", "profile", "roles"));
    addUserToGroup(addToGroupUrl, otherAdminToken.access, otherUser.email, "users")
        .then()
        .body("error", nullValue())
        .statusCode(201);
    TokenPair otherUserToken = getUserTokenWith(otherResourceClient, otherUser.email, otherUser.password,
        Arrays.asList("openid", "email", "profile", "roles"));
    given()
        .header("Authorization", "Bearer " + otherUserToken.access)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"users\", \"add\": true}", userToAddByOtherAdmin.email))
        .when()
        .post(addToGroupUrl)
        .then()
        .body("error", equalTo("User does not have resource-admin role"))
        .statusCode(403);
  }

  @Test
  public void testHasAdminOnEmptyClient() {
    ClientPair emptyResourceClient = registerClientAndReturnClientPair();

    String token = getTokenForClient(emptyResourceClient);
    given()
        .header("Authorization", "Bearer " + token)
        .when()
        .get(hasAdminUrl)
        .then()
        .body("error", nullValue())
        .statusCode(200)
        .body("hasAdmin", equalTo(false));
  }

  @Test
  public void testHasAdminOnClientWithAdmin() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getTokenForClient(mainResourceClient);
    given()
        .header("Authorization", "Bearer " + token)
        .when()
        .get(hasAdminUrl)
        .then()
        .statusCode(200)
        .body("hasAdmin", equalTo(true));
  }

  @Test
  public void testHasAdminReturnsUnauthorizedIfCalledWithNonServiceToken() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getUserTokenWith(mainResourceClient, mainAdminUser.email, mainAdminUser.password);
    given()
        .header("Authorization", "Bearer " + token)
        .when()
        .get(hasAdminUrl)
        .then()
        .statusCode(401)
        .body("error", equalTo("not a service account token"));
  }

  // ========================================
  // HIERARCHICAL GROUP ROLE TESTS
  // ========================================

  @Test
  public void testUsersWithNoRolesHaveNoResourceAccess() {
    ClientPair testClient = registerClientAndReturnClientPair();
    TestUser userWithNoRoles = createUser();
    List<String> scopes = Arrays.asList("openid", "email", "profile", "roles");

    TokenPair tokenPair = getUserTokenWith(testClient, userWithNoRoles.email, userWithNoRoles.password, scopes);
    DecodedJWT jwt = JWT.decode(tokenPair.access);

    // Verify scope claim is correct
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split("\\s+"));
    assertEquals(new HashSet<>(scopes), new HashSet<>(claimScopes));

    // Verify no resource_access claim exists for users with no roles
    Claim resourceAccessClaim = jwt.getClaim("resource_access");
    assertTrue(resourceAccessClaim.isMissing(), "Users with no roles should not have resource_access claim");

    // Test refresh token flow maintains the same behavior
    TokenPair newTokenPair = refreshTokenFlow(testClient, tokenPair.refresh);
    DecodedJWT newJwt = JWT.decode(newTokenPair.access);
    Claim newResourceAccessClaim = newJwt.getClaim("resource_access");
    assertTrue(newResourceAccessClaim.isMissing(),
        "Users with no roles should not have resource_access claim after refresh");
  }

  @ParameterizedTest
  @MethodSource("userRoleProvider")
  @SuppressWarnings("unchecked")
  public void testUsersWithRolesHaveCorrectResourceAccess(String roleLevel, List<String> expectedRoles) {
    ClientPair testClient = registerClientAndReturnClientPair();
    TestUser userWithRole = createUserWithRole(testClient.clientId, roleLevel);
    List<String> scopes = Arrays.asList("openid", "email", "profile", "roles");

    TokenPair tokenPair = getUserTokenWith(testClient, userWithRole.email, userWithRole.password, scopes);
    DecodedJWT jwt = JWT.decode(tokenPair.access);

    // Verify scope claim is correct
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split("\\s+"));
    assertEquals(new HashSet<>(scopes), new HashSet<>(claimScopes));

    // Verify resource_access claim exists and has correct roles
    Claim resourceAccessClaim = jwt.getClaim("resource_access");
    assertTrue(!resourceAccessClaim.isMissing(), "Users with roles should have resource_access claim");

    Map<String, Claim> resourceAccess = (Map<String, Claim>) resourceAccessClaim
        .asMap().get(testClient.clientId);
    List<String> resourceRoles = (List<String>) resourceAccess.get("roles");
    assertEquals(expectedRoles.size(), resourceRoles.size(),
        "Should have exactly " + expectedRoles.size() + " roles for " + roleLevel);
    assertTrue(resourceRoles.containsAll(expectedRoles),
        "Should contain all expected roles: " + expectedRoles + " but got: " + resourceRoles);

    // Test refresh token flow maintains the same roles
    TokenPair newTokenPair = refreshTokenFlow(testClient, tokenPair.refresh);
    DecodedJWT newJwt = JWT.decode(newTokenPair.access);
    Claim newResourceAccessClaim = newJwt.getClaim("resource_access");
    Map<String, Claim> newResourceAccess = (Map<String, Claim>) newResourceAccessClaim
        .asMap().get(testClient.clientId);
    List<String> newResourceRoles = (List<String>) newResourceAccess.get("roles");
    assertEquals(expectedRoles.size(), newResourceRoles.size(),
        "Should maintain same number of roles after refresh");
    assertTrue(newResourceRoles.containsAll(expectedRoles),
        "Should maintain same roles after refresh");
  }

  private static Stream<Arguments> userRoleProvider() {
    return Stream.of(
        Arguments.of(RESOURCE_USER, Arrays.asList("resource_user")), // Basic user role
        Arguments.of(RESOURCE_POWER_USER, Arrays.asList("resource_user", "resource_power_user")),
        Arguments.of(RESOURCE_MANAGER, Arrays.asList("resource_user", "resource_power_user", "resource_manager")),
        Arguments.of(RESOURCE_ADMIN, Arrays.asList("resource_user", "resource_power_user", "resource_manager",
            "resource_admin")));
  }

  // ========================================
  // HELPER METHODS
  // ========================================

  private RealmResource realm = keycloak.getKeycloakAdminClient().realm(REALM);

  private ClientPair createClientWithoutGroups() {
    long timestamp = System.currentTimeMillis();
    String clientId = "test-missing-groups-" + timestamp;

    org.keycloak.representations.idm.ClientRepresentation client = new org.keycloak.representations.idm.ClientRepresentation();
    client.setClientId(clientId);
    client.setEnabled(true);
    client.setClientAuthenticatorType("client-secret");
    client.setSecret("change-me");
    client.setRedirectUris(Arrays.asList("http://localhost/callback"));
    client.setWebOrigins(Arrays.asList("+"));
    client.setBearerOnly(false);
    client.setConsentRequired(false);
    client.setStandardFlowEnabled(true);
    client.setImplicitFlowEnabled(false);
    client.setDirectAccessGrantsEnabled(true);
    client.setServiceAccountsEnabled(true);
    client.setPublicClient(false);
    client.setProtocol("openid-connect");
    client.setFullScopeAllowed(false);

    // Create the client without groups (this is the key difference)
    jakarta.ws.rs.core.Response response = realm.clients().create(client);
    assertThat("Failed to create client without groups: " + response.getStatus() + " " + response.getStatusInfo(),
        response.getStatus(), is(201));

    return new ClientPair(clientId, "change-me");
  }

  private void setupAdminUser(ClientPair clientPair, TestUser user) {
    String serviceAccountToken = getTokenForClient(clientPair);
    makeResourceAdmin(serviceAccountToken, user.email)
        .then()
        .statusCode(201);
  }
}