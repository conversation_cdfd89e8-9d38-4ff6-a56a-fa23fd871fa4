package com.bodhisearch;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import com.bodhisearch.templates.RealmConfigGenerator;

import dasniko.testcontainers.keycloak.KeycloakContainer;

/**
 * Test class for validating the complete realm seeding workflow.
 * 
 * This class tests the entire process from FreeMarker template generation
 * through JSON creation to Keycloak import and validation:
 * 
 * 1. FreeMarker template (.ftl) processing
 * 2. JSON realm configuration generation
 * 3. Keycloak CLI import process
 * 4. Validation of seeded data in Keycloak
 * 5. Basic functionality testing of seeded components
 */
@Testcontainers
public class RealmSeedingTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(RealmSeedingTest.class);

  public static final String REALM = "bodhi";
  public static final String PROVIDER_ID = "bodhi-resource";

  @Container
  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:23.0.7")
      .withFeaturesEnabled("admin-fine-grained-authz", "token-exchange")
      .withProviderClassesFrom("target/classes");

  private static String registerClientUrl;
  private static String resourceAdminUrl;
  private static RealmResource realm;

  @BeforeAll
  public static void setupRealmSeeding() {
    // Test FreeMarker template generation
    RealmConfigGenerator.generate("./src/test/resources/import-files/bodhi-realm-test-generated.json");

    // Test CLI import process
    importFile(keycloak.getAuthServerUrl(), keycloak.getAdminUsername(),
        keycloak.getAdminPassword(),
        "./src/test/resources/import-files/bodhi-realm-generated.json");

    // Setup URLs and admin client
    registerClientUrl = String.format("%s/realms/%s/%s/clients/new", keycloak.getAuthServerUrl(), REALM, PROVIDER_ID);
    resourceAdminUrl = String.format("%s/realms/%s/%s/clients/make-resource-admin", keycloak.getAuthServerUrl(), REALM,
        PROVIDER_ID);

    Keycloak adminClient = KeycloakBuilder.builder()
        .serverUrl(keycloak.getAuthServerUrl())
        .realm("master")
        .clientId("admin-cli")
        .username(keycloak.getAdminUsername())
        .password(keycloak.getAdminPassword())
        .build();
    realm = adminClient.realm(REALM);
  }

  @Test
  public void testTemplateGenerationAndCliImport() {
    // This test validates that the CLI import process worked correctly
    // by checking that we can interact with the seeded realm
    LOGGER.info("Testing CLI import success by verifying realm accessibility");

    // Basic validation that the realm is accessible and configured
    assertNotNull(keycloak.getAuthServerUrl(), "Auth server URL should be available");
    assertTrue(keycloak.getAuthServerUrl().contains("localhost"), "Should be running on localhost");
  }

  @Test
  public void testTemplateGenerationWorksCorrectly() {
    // Test that FreeMarker template generation produces a valid JSON file
    String testGeneratedFile = "./src/test/resources/import-files/bodhi-realm-test-generated.json";
    RealmConfigGenerator.generate(testGeneratedFile);

    // Verify file exists (basic check that generation worked)
    java.io.File generatedFile = new java.io.File(testGeneratedFile);
    assertTrue(generatedFile.exists(), "Generated file should exist");
    assertTrue(generatedFile.length() > 0, "Generated file should not be empty");

    LOGGER.info("Template generation test completed successfully");
  }

  @Test
  public void testCliImportValidation() {
    // Test that CLI import process can validate realm structure
    List<UserRepresentation> users = realm.users().list();
    assertNotNull(users, "Should be able to list users from seeded realm");

    // Check that realm is properly configured
    assertTrue(realm.toRepresentation().isEnabled(), "Realm should be enabled");
    assertEquals(REALM, realm.toRepresentation().getRealm(), "Realm name should match");

    LOGGER.info("CLI import validation completed successfully");
  }

  @Test
  public void testSeedingGeneratedCorrectRealm() {
    // Test that seeding process created the correct realm configuration
    LOGGER.info("Validating seeded realm configuration");

    // Basic realm structure validation
    assertNotNull(realm.toRepresentation(), "Should have realm representation");
    assertTrue(realm.toRepresentation().isEnabled(), "Realm should be enabled");
    assertEquals(REALM, realm.toRepresentation().getRealm(), "Realm name should be correct");

    // Verify basic realm settings from generated config
    assertTrue(realm.toRepresentation().isRegistrationEmailAsUsername(),
        "Should have email as username enabled");
    assertTrue(realm.toRepresentation().isRememberMe(), "Should have remember me enabled");
    assertFalse(realm.toRepresentation().isVerifyEmail(), "Should have verify email enabled");
    assertTrue(realm.toRepresentation().isLoginWithEmailAllowed(),
        "Should allow login with email");

    LOGGER.info("Seeded realm validation completed successfully");
  }

  // ========================================
  // HELPER METHODS
  // ========================================

  public static void importFile(String keycloakUrl, String username, String password, String filename) {
    try {
      String[] command = { "java", "-jar",
          "tools/keycloak-config-cli-23.0.7.jar",
          "--import.files.locations=" + filename,
          "--keycloak.url=" + keycloakUrl,
          "--keycloak.user=" + username,
          "--keycloak.password=" + password,
          "--logging.level.keycloak-config-cli=debug"
      };

      Process process = Runtime.getRuntime().exec(command);
      BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
      BufferedReader stderrReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
      String line;

      while ((line = stdoutReader.readLine()) != null) {
        LOGGER.info(line);
      }

      while ((line = stderrReader.readLine()) != null) {
        LOGGER.error(line);
      }

      process.waitFor();
      int exitCode = process.exitValue();

      if (exitCode == 0) {
        LOGGER.info("CLI import executed successfully");
      } else {
        String errMsg = "CLI import failed with exit code: " + exitCode;
        LOGGER.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    } catch (IOException | InterruptedException e) {
      LOGGER.error("Error during CLI import", e);
      throw new RuntimeException(e);
    }
  }
}