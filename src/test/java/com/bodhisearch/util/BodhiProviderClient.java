package com.bodhisearch.util;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

import java.util.Arrays;
import java.util.List;

import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;

public class BodhiProviderClient {
  private final String registerClientUrl;
  private final String resourceAdminUrl;
  private final String addToGroupUrl;
  private final String hasAdminUrl;

  public BodhiProviderClient(String keycloakBaseUrl, String realm, String providerId) {
    this.registerClientUrl = String.format("%s/realms/%s/%s/clients", keycloakBaseUrl, realm, providerId);
    this.resourceAdminUrl = String.format("%s/realms/%s/%s/clients/make-resource-admin", keycloakBaseUrl, realm,
        providerId);
    this.addToGroupUrl = String.format("%s/realms/%s/%s/clients/add-user-to-group", keycloakBaseUrl, realm,
        providerId);
    this.hasAdminUrl = String.format("%s/realms/%s/%s/clients/has-resource-admin", keycloakBaseUrl, realm,
        providerId);
  }

  public JsonPath registerClient() {
    return registerClient("http://localhost:8090/");
  }

  public JsonPath registerClient(String redirectUri) {
    return registerClient(Arrays.asList(redirectUri));
  }

  public JsonPath registerClient(List<String> redirectUris) {
    // Build JSON body with redirect URIs
    StringBuilder jsonBuilder = new StringBuilder();
    jsonBuilder.append("{\"redirect_uris\": [");
    for (int i = 0; i < redirectUris.size(); i++) {
      if (i > 0) {
        jsonBuilder.append(", ");
      }
      jsonBuilder.append("\"").append(redirectUris.get(i)).append("\"");
    }
    jsonBuilder.append("]}");

    JsonPath response = given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .contentType(ContentType.JSON)
        .body(jsonBuilder.toString())
        .when()
        .post(registerClientUrl)
        .then()
        .body("error", nullValue())
        .statusCode(201)
        .body("client_id", notNullValue())
        .body("client_secret", notNullValue())
        .extract()
        .jsonPath();
    return response;
  }

  public ClientPair registerClientAndReturnClientPair() {
    JsonPath response = registerClient();
    return new ClientPair(response.getString("client_id"), response.getString("client_secret"));
  }

  public ClientPair registerClientAndReturnClientPair(String redirectUri) {
    JsonPath response = registerClient(redirectUri);
    return new ClientPair(response.getString("client_id"), response.getString("client_secret"));
  }

  public ClientPair registerClientAndReturnClientPair(List<String> redirectUris) {
    JsonPath response = registerClient(redirectUris);
    return new ClientPair(response.getString("client_id"), response.getString("client_secret"));
  }

  public Response makeResourceAdmin(String token, String userToMakeFirstAdmin) {
    return given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", userToMakeFirstAdmin))
        .when()
        .post(resourceAdminUrl);
  }

  public Response addUserToGroup(String token, String username, String group) {
    return given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"%s\"}", username, group))
        .when()
        .post(addToGroupUrl);
  }

  public Response hasResourceAdmin(String token, String username) {
    return given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", username))
        .when()
        .post(hasAdminUrl);
  }

  public Response registerClientWithTestPrefix(List<String> redirectUris) {
    // Build JSON body with redirect URIs
    StringBuilder jsonBuilder = new StringBuilder();
    jsonBuilder.append("{\"redirect_uris\": [");
    for (int i = 0; i < redirectUris.size(); i++) {
      if (i > 0) {
        jsonBuilder.append(", ");
      }
      jsonBuilder.append("\"").append(redirectUris.get(i)).append("\"");
    }
    jsonBuilder.append("]}");

    return given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .contentType(ContentType.JSON)
        .body(jsonBuilder.toString())
        .when()
        .post(registerClientUrl + "?live_test=true");
  }

  public ClientPair registerClientWithTestPrefixAndReturnClientPair(List<String> redirectUris) {
    Response response = registerClientWithTestPrefix(redirectUris);
    response.then()
        .statusCode(201)
        .body("error", nullValue())
        .body("client_id", notNullValue())
        .body("client_secret", notNullValue());
    
    return new ClientPair(
        response.jsonPath().getString("client_id"), 
        response.jsonPath().getString("client_secret")
    );
  }
}