package com.bodhisearch.util;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.keycloak.admin.client.Keycloak;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;

public class KeycloakAdminClient {
  private static final Logger LOGGER = LoggerFactory.getLogger(KeycloakAdminClient.class);
  private final String tokenUrl;

  public KeycloakAdminClient(String keycloakBaseUrl, String realm, String providerId, Keycloak admin) {
    this.tokenUrl = String.format("%s/realms/%s/protocol/openid-connect/token", keycloakBaseUrl, realm);
  }

  public String getTokenForClient(ClientPair clientPair) {
    Response response = given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "client_credentials")
        .formParam("client_id", clientPair.clientId)
        .formParam("client_secret", clientPair.clientSecret)
        .when()
        .post(tokenUrl);
    return response
        .then()
        .statusCode(200)
        .contentType(ContentType.JSON)
        .body("error", nullValue())
        .body("access_token", notNullValue())
        .extract()
        .jsonPath().getString("access_token");
  }

  public Response getUserTokenResponseWith(ClientPair clientPair, String username,
      String password, List<String> scopes) {
    RequestSpecification request = given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "password")
        .formParam("client_id", clientPair.clientId)
        .formParam("client_secret", clientPair.clientSecret)
        .formParam("username", username)
        .formParam("password", password)
        .formParam("scope", scopes.stream().collect(Collectors.joining(" ")));
    Response response = request
        .when()
        .post(tokenUrl);
    return response;
  }

  public TokenPair getUserTokenWith(ClientPair clientPair, String username,
      String password, List<String> scopes) {
    Response response = getUserTokenResponseWith(clientPair, username, password, scopes);

    if (response.getStatusCode() != 200) {
      LOGGER.error("Error response: {}", response.asString());
      throw new RuntimeException(String.format("Failed to obtain token. Status code: %d, Message: %s",
          response.getStatusCode(), response.asString()));
    }
    return new TokenPair(response.jsonPath().getString("access_token"), response.jsonPath().getString("refresh_token"));
  }

  public String getUserTokenWith(ClientPair clientPair, String username, String password) {
    return getUserTokenWith(clientPair, username, password, Arrays.asList("openid")).access;
  }

  public TokenPair refreshTokenFlow(ClientPair clientPair, String refreshToken) {
    Response response = given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .contentType(ContentType.URLENC)
        .formParam("grant_type", "refresh_token")
        .formParam("client_id", clientPair.clientId)
        .formParam("client_secret", clientPair.clientSecret)
        .formParam("refresh_token", refreshToken)
        .when()
        .post(tokenUrl);
    if (response.getStatusCode() != 200) {
      LOGGER.error("Error response: {}", response.asString());
      throw new RuntimeException(String.format("Failed to obtain token. Status code: %d, Message: %s",
          response.getStatusCode(), response.asString()));
    }
    return new TokenPair(response.jsonPath().getString("access_token"),
        response.jsonPath().getString("refresh_token"));
  }
}