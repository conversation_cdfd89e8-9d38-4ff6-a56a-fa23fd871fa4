package com.bodhisearch.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.restassured.RestAssured;
import io.restassured.specification.RequestSpecification;

public class KeycloakTestUtils {
  private static final Logger LOGGER = LoggerFactory.getLogger(KeycloakTestUtils.class);

  public static RequestSpecification given() {
    return RestAssured.given().relaxedHTTPSValidation().redirects().follow(false);
  }

  public static void importFile(String keycloakUrl, String username, String password, String filename) {
    try {
      String[] command = { "java", "-jar",
          "tools/keycloak-config-cli-23.0.7.jar",
          "--import.files.locations=" + filename,
          "--keycloak.url=" + keycloakUrl,
          "--keycloak.user=" + username,
          "--keycloak.password=" + password,
      };
      Process process = Runtime.getRuntime().exec(command);
      BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
      BufferedReader stderrReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
      String line;

      while ((line = stdoutReader.readLine()) != null) {
        LOGGER.info(line);
      }

      while ((line = stderrReader.readLine()) != null) {
        LOGGER.error(line);
      }
      process.waitFor();

      int exitCode = process.exitValue();
      if (exitCode == 0) {
        LOGGER.info("CLI executed successfully.");
      } else {
        String errMsg = "CLI execution failed with exit code: " + exitCode;
        LOGGER.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    } catch (IOException | InterruptedException e) {
      LOGGER.error("Error importing file", e);
      throw new RuntimeException(e);
    }
  }
}