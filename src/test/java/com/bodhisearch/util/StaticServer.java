package com.bodhisearch.util;

import java.io.IOException;
import java.net.ServerSocket;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.server.handler.ResourceHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * A lightweight static file server using Jetty for testing purposes.
 * Supports dynamic port allocation and easy lifecycle management.
 */
public class StaticServer {
    private static final Logger LOGGER = LoggerFactory.getLogger(StaticServer.class);
    
    private Server server;
    private int port;
    private String baseUrl;
    private final String resourceDirectory;
    
    public StaticServer(String resourceDirectory) {
        this.resourceDirectory = resourceDirectory;
    }
    
    /**
     * Start the server on an available port
     * @return the port number the server is listening on
     * @throws Exception if server fails to start
     */
    public int start() throws Exception {
        // Find an available port
        this.port = findAvailablePort();
        
        // Create Jetty server
        server = new Server(port);
        
        // Create resource handler for static files
        ResourceHandler resourceHandler = new ResourceHandler();
        
        // Resolve the resource directory path
        Path resourcePath = Paths.get(resourceDirectory).toAbsolutePath();
        resourceHandler.setResourceBase(resourcePath.toString());
        resourceHandler.setDirectoriesListed(false);
        resourceHandler.setWelcomeFiles(new String[]{"index.html"});
        
        server.setHandler(resourceHandler);
        
        // Start the server
        server.start();
        
        this.baseUrl = "http://localhost:" + port;
        
        LOGGER.info("Static server started on port {} serving directory: {}", port, resourcePath);
        LOGGER.info("Server URL: {}", baseUrl);
        
        return port;
    }
    
    /**
     * Stop the server
     */
    public void stop() {
        if (server != null) {
            try {
                server.stop();
                LOGGER.info("Static server stopped");
            } catch (Exception e) {
                LOGGER.error("Error stopping static server", e);
            }
        }
    }
    
    /**
     * Get the port the server is listening on
     * @return the port number
     */
    public int getPort() {
        return port;
    }
    
    /**
     * Get the base URL of the server
     * @return the base URL (e.g., "http://localhost:8090")
     */
    public String getBaseUrl() {
        return baseUrl;
    }
    
    /**
     * Check if the server is running
     * @return true if server is running, false otherwise
     */
    public boolean isRunning() {
        return server != null && server.isRunning();
    }
    
    /**
     * Find an available port by creating a temporary ServerSocket
     * @return an available port number
     * @throws IOException if no port is available
     */
    private int findAvailablePort() throws IOException {
        try (ServerSocket socket = new ServerSocket(0)) {
            socket.setReuseAddress(true);
            return socket.getLocalPort();
        }
    }
} 