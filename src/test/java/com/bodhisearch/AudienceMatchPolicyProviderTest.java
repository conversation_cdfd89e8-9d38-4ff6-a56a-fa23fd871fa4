package com.bodhisearch;

import static com.bodhisearch.AudienceMatchPolicyProvider.verifyTokenWith;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_USER;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.in;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.stream.Stream;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.keycloak.common.VerificationException;
import org.keycloak.representations.AccessToken;
import org.keycloak.representations.idm.KeysMetadataRepresentation.KeyMetadataRepresentation;

import com.bodhisearch.util.ClientPair;
import com.bodhisearch.util.TokenPair;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;

import io.restassured.response.Response;

/**
 * Test class for AudienceMatchPolicyProvider functionality.
 * 
 * This class tests the core audience matching logic that validates token
 * exchanges by ensuring the Authorization header contains a valid service
 * account token
 * that matches the expected audience.
 */
public class AudienceMatchPolicyProviderTest extends BaseTest {

  // ========================================
  // SUCCESSFUL TOKEN EXCHANGE TESTS
  // ========================================

  @Test
  public void testValidTokenExchange() {
    // Setup dynamic test data
    ClientPair appClient = createPublicClientForUser();
    ClientPair targetResourceClient = registerClientAndReturnClientPair();
    TestUser testUser = createUserWithRole(targetResourceClient.clientId, RESOURCE_USER);

    // Test comprehensive token exchange flow
    TokenPair tokenPair = getUserTokenWith(appClient, testUser.email, testUser.password,
        Arrays.asList("openid", "email", "roles"));
    String resourceToken = getTokenForClient(targetResourceClient);
    assertThat(resourceToken, notNullValue());

    // Perform token exchange
    Response exchangeResponse = exchangeTokenResponse(appClient.clientId, tokenPair.access,
        targetResourceClient.clientId, String.format("Bearer %s", resourceToken));
    exchangeResponse.then().body("error_description", nullValue()).statusCode(200);

    // Validate exchanged token properties
    String exchangedToken = exchangeResponse.jsonPath().getString("access_token");
    assertNotNull(exchangedToken);
    AccessToken token = decodeToken(exchangedToken);

    // Verify audience and user information
    assertThat(targetResourceClient.clientId, in(token.getAudience()));
    assertEquals(testUser.email, token.getPreferredUsername());
    assertTrue(token.getResourceAccess().get(targetResourceClient.clientId).getRoles().contains("resource_user"));
  }

  // ========================================
  // AUTHORIZATION HEADER VALIDATION TESTS
  // ========================================

  @FunctionalInterface
  interface HeaderGenerator {
    String generate(String userToken, String resourceToken);
  }

  private static Stream<Arguments> invalidAuthHeaderProvider() {
    return Stream.of(
        Arguments.of((HeaderGenerator) (u, r) -> null, "null_header"),
        Arguments.of((HeaderGenerator) (u, r) -> "", "empty_string"),
        Arguments.of((HeaderGenerator) (u, r) -> "Bearer", "bearer_only"),
        Arguments.of((HeaderGenerator) (u, r) -> "Bearer ", "empty_bearer"),
        Arguments.of((HeaderGenerator) (u, r) -> "bearer " + r, "lowercase_bearer"),
        Arguments.of((HeaderGenerator) (u, r) -> "Basic " + r, "basic_auth"),
        Arguments.of((HeaderGenerator) (u, r) -> "Bearer  " + r, "double_space"),
        Arguments.of((HeaderGenerator) (u, r) -> "Bearer " + u, "user_token_as_bearer"),
        Arguments.of((HeaderGenerator) (u, r) -> u, "user_token_raw"),
        Arguments.of((HeaderGenerator) (u, r) -> r, "resource_token_raw"),
        Arguments.of((HeaderGenerator) (u, r) -> "Bearer null", "bearer_null"),
        Arguments.of((HeaderGenerator) (u, r) -> "Bearer invalid-token", "bearer_invalid"),
        Arguments.of((HeaderGenerator) (u, r) -> "Token " + r, "wrong_prefix"));
  }

  @ParameterizedTest
  @MethodSource("invalidAuthHeaderProvider")
  public void testTokenExchangeFailsWithInvalidAuthorizationHeaders(HeaderGenerator headerGenerator,
      String testCaseName) {
    // Setup test data locally
    ClientPair appClient = createPublicClientForUser();
    ClientPair targetResourceClient = registerClientAndReturnClientPair();
    TestUser testUser = createUserWithRole(targetResourceClient.clientId, RESOURCE_USER);

    String userToken = getUserTokenWith(appClient, testUser.email, testUser.password,
        Arrays.asList("openid")).access;
    String resourceToken = getTokenForClient(targetResourceClient);
    String authHeader = headerGenerator.generate(userToken, resourceToken);

    Response exchangeResponse = exchangeTokenResponse(appClient.clientId, userToken, targetResourceClient.clientId,
        authHeader);
    exchangeResponse.then().statusCode(403).body("error", equalTo("access_denied")).body("error_description",
        equalTo("Client not allowed to exchange"));
  }

  // ========================================
  // PERMISSION AND AUTHORIZATION TESTS
  // ========================================

  @Test
  public void testResourceMissingTokenExchangePermission() {
    ClientPair appClient = createPublicClientForUser();
    ClientPair noExchangePermissionClient = createClientWithoutTokenExchangePermission();
    TestUser testUser = createUser();

    String userToken = getUserTokenWith(appClient, testUser.email, testUser.password,
        Arrays.asList("openid")).access;
    String resourceToken = getTokenForClient(noExchangePermissionClient);

    exchangeTokenResponse(appClient.clientId, userToken, noExchangePermissionClient.clientId,
        String.format("Bearer %s", resourceToken))
        .then()
        .statusCode(403)
        .body("error_description", equalTo("Client not allowed to exchange"));
  }

  // ========================================
  // AUDIENCE MISMATCH TESTS
  // ========================================

  @Test
  public void testTokenExchangeFailsIfAudienceDoesNotMatch() {
    ClientPair appClient = createPublicClientForUser();
    ClientPair targetResourceClient = registerClientAndReturnClientPair();
    ClientPair otherResourceClient = registerClientAndReturnClientPair();
    TestUser testUser = createUserWithRole(targetResourceClient.clientId, RESOURCE_USER);

    String userToken = getUserTokenWith(appClient, testUser.email, testUser.password,
        Arrays.asList("openid")).access;
    String resourceToken = getTokenForClient(otherResourceClient);

    Response exchangeResponse = exchangeTokenResponse(appClient.clientId, userToken, targetResourceClient.clientId,
        String.format("Bearer %s", resourceToken));
    exchangeResponse.then().statusCode(403).body("error", equalTo("access_denied")).body("error_description",
        equalTo("Client not allowed to exchange"));
  }

  // ========================================
  // DIRECT TOKEN VERIFICATION TESTS
  // ========================================

  @Test
  public void testVerifyTokenWithSucceeds()
      throws JsonMappingException, JsonProcessingException, InvalidKeySpecException, NoSuchAlgorithmException,
      VerificationException {
    ClientPair targetResourceClient = registerClientAndReturnClientPair();

    String userToken = getTokenForClient(targetResourceClient);
    KeyMetadataRepresentation keyRepr = getKeyRepr(userToken);

    byte[] publicKeyDER = Base64.getDecoder().decode(keyRepr.getPublicKey());
    KeyFactory keyFactory = KeyFactory.getInstance("RSA");
    X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyDER);
    PublicKey publicKey = keyFactory.generatePublic(keySpec);
    String issuer = String.format("%s/realms/%s", keycloak.getAuthServerUrl(), REALM);

    // This should succeed - verifying token with matching audience
    AccessToken result = verifyTokenWith(userToken, issuer, targetResourceClient.clientId, publicKey);
    assertNotNull(result);
    assertEquals(targetResourceClient.clientId, result.getIssuedFor());
  }

  // ========================================
  // EDGE CASE TESTS (Additional Coverage)
  // ========================================

  @Test
  public void testTokenExchangeWithMalformedToken() {
    ClientPair appClient = createPublicClientForUser();
    ClientPair targetResourceClient = registerClientAndReturnClientPair();
    TestUser testUser = createUserWithRole(targetResourceClient.clientId, RESOURCE_USER);

    String userToken = getUserTokenWith(appClient, testUser.email, testUser.password,
        Arrays.asList("openid")).access;
    String malformedToken = "not.a.valid.jwt.token.format";

    Response exchangeResponse = exchangeTokenResponse(appClient.clientId, userToken, targetResourceClient.clientId,
        String.format("Bearer %s", malformedToken));
    exchangeResponse.then().statusCode(403).body("error", equalTo("access_denied"));
  }

  @Test
  public void testTokenExchangeWithEmptyAudience() {
    ClientPair appClient = createPublicClientForUser();
    ClientPair targetResourceClient = registerClientAndReturnClientPair();
    TestUser testUser = createUserWithRole(targetResourceClient.clientId, RESOURCE_USER);

    String userToken = getUserTokenWith(appClient, testUser.email, testUser.password,
        Arrays.asList("openid")).access;
    String resourceToken = getTokenForClient(targetResourceClient);

    // Try token exchange with empty audience parameter - this returns 400 (bad
    // request)
    Response exchangeResponse = exchangeTokenResponse(appClient.clientId, userToken, "",
        String.format("Bearer %s", resourceToken));
    exchangeResponse.then().statusCode(400);
  }

  @Test
  public void testTokenExchangeWithWrongClientIdInResourceToken() {
    ClientPair appClient = createPublicClientForUser();
    ClientPair targetResourceClient = registerClientAndReturnClientPair();
    ClientPair wrongClient = registerClientAndReturnClientPair();
    TestUser testUser = createUserWithRole(targetResourceClient.clientId, RESOURCE_USER);

    String userToken = getUserTokenWith(appClient, testUser.email, testUser.password,
        Arrays.asList("openid")).access;
    String wrongResourceToken = getTokenForClient(wrongClient);

    // Use token from wrong client as authorization
    Response exchangeResponse = exchangeTokenResponse(appClient.clientId, userToken, targetResourceClient.clientId,
        String.format("Bearer %s", wrongResourceToken));
    exchangeResponse.then().statusCode(403).body("error", equalTo("access_denied"));
  }
}
