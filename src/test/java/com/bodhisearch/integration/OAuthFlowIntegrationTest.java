package com.bodhisearch.integration;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import java.util.Base64;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.bodhisearch.util.ClientPair;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.AriaRole;
import com.microsoft.playwright.options.LoadState;

import io.restassured.response.Response;

public class OAuthFlowIntegrationTest extends BaseIntegrationTest {

  private static final Logger LOGGER = LoggerFactory.getLogger(OAuthFlowIntegrationTest.class);
  private static final ObjectMapper mapper = new ObjectMapper();

  @BeforeAll
  public static void setupTestApp() throws Exception {
    // Base setup is already done in BaseIntegrationTest including test app server
    LOGGER.info("OAuth flow integration test setup - test app server already running");
  }

  @AfterAll
  public static void tearDownTestApp() {
    // Test app server cleanup is handled by BaseIntegrationTest
    LOGGER.info("OAuth flow integration test teardown");
  }

  @Test
  public void testCompleteOAuthFlow() throws Exception {
    LOGGER.info("Starting complete OAuth flow integration test");

    // 1. Get test app URL and register client using provider endpoint with correct
    // redirect URI
    String testAppUrl = getTestAppUrl();
    String redirectUri = testAppUrl + "/index.html";
    ClientPair clientPair = registerClientAndReturnClientPair(redirectUri);
    String clientId = clientPair.clientId;
    String clientSecret = clientPair.clientSecret;

    LOGGER.info("Registered OAuth client with ID: {} for redirect URI: {}", clientId, redirectUri);

    // 2. Generate test user details
    String username = "testuser" + ThreadLocalRandom.current().nextInt(1000, 9999);
    String password = "TestPassword123!";
    String email = username + "@example.com";
    String firstName = "Test";
    String lastName = "User";

    LOGGER.info("Generated test user: {} with email: {}", username, email);

    try (Playwright playwright = Playwright.create()) {
      Browser browser = playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(false));
      BrowserContext context = browser.newContext();
      Page page = context.newPage();

      // 3. Navigate to dummy OAuth app (test app)
      LOGGER.info("Navigating to test app at: {}", testAppUrl);
      page.navigate(testAppUrl);
      page.waitForLoadState(LoadState.NETWORKIDLE);

      // 4. Configure OAuth client details in the test app
      page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Keycloak URL:")).fill(keycloakBaseUrl);
      page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Client ID:")).fill(clientId);
      page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Client Secret:")).fill(clientSecret);

      LOGGER.info("Configured OAuth client in test app");

      // 5. Start OAuth flow
      page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Start OAuth Flow")).click();
      LOGGER.info("Started OAuth flow");

      // 6. Wait for redirect to Keycloak login page and sign up new user
      page.waitForURL(url -> url.contains(keycloakBaseUrl + "/realms/" + REALM + "/protocol/openid-connect/auth"));
      LOGGER.info("Redirected to Keycloak auth page");

      // 7. Click on Register link to create new user (sign up)
      page.getByRole(AriaRole.LINK, new Page.GetByRoleOptions().setName("Register")).click();
      LOGGER.info("Clicked registration link");

      // Wait for registration form to load
      page.waitForLoadState(LoadState.NETWORKIDLE);

      // 8. Fill registration form using proper selectors
      page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("First name")).fill(firstName);
      page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Last name")).fill(lastName);
      page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Email")).fill(email);
      page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Password").setExact(true)).fill(password);
      page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Confirm password")).fill(password);

      LOGGER.info("Filled user registration form");

      // 9. Submit registration
      page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Register")).click();
      LOGGER.info("Submitted user registration");

      // 10. Wait for redirect back to test app
      page.waitForURL(url -> url.startsWith(testAppUrl));
      LOGGER.info("Redirected back to test app");

      // 11. Wait for OAuth callback processing and get access token
      page.waitForSelector("#access-token", new Page.WaitForSelectorOptions().setTimeout(10000));

      // 12. Extract access token from the page
      String accessToken = page.textContent("#access-token");
      assertThat("Access token should not be null", accessToken, notNullValue());
      assertThat("Access token should not be empty", accessToken.length() > 0, is(true));

      LOGGER.info("Successfully obtained access token: {}", accessToken.substring(0, 50) + "...");

      // 13. Validate JWT token structure and basic claims
      validateJWTToken(accessToken);
      LOGGER.info("JWT token validation passed");

      // 14. Make user admin via /clients/make-resource-admin endpoint
      String clientToken = getTokenForClient(clientPair);
      Response adminResponse = makeResourceAdmin(clientToken, email);
      adminResponse.then().statusCode(201);

      LOGGER.info("Successfully made user {} admin via provider endpoint", email);
    }
  }

  private void validateJWTToken(String token) throws JsonMappingException, JsonProcessingException {
    // Decode JWT token (assuming it's a standard JWT with 3 parts)
    String[] parts = token.split("\\.");
    assertThat("JWT should have 3 parts", parts.length, is(3));

    // Decode the payload (second part)
    String payload = new String(Base64.getUrlDecoder().decode(parts[1]));
    JsonNode payloadNode = mapper.readTree(payload);

    // Validate basic token structure
    assertThat("Token should have 'sub' claim", payloadNode.has("sub"), is(true));
    assertThat("Token should have 'iss' claim", payloadNode.has("iss"), is(true));
    assertThat("Token should have 'exp' claim", payloadNode.has("exp"), is(true));
    // Note: 'aud' claim is optional for access tokens

    // Validate issuer
    String expectedIssuer = keycloakBaseUrl + "/realms/" + REALM;
    assertThat("Token issuer should match", payloadNode.get("iss").asText(), is(expectedIssuer));

    LOGGER.info("JWT token basic validation passed");
  }
}