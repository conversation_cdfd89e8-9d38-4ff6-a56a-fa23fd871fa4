package com.bodhisearch.integration;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import com.bodhisearch.templates.RealmConfigGenerator;
import com.bodhisearch.util.BodhiProviderClient;
import com.bodhisearch.util.ClientPair;
import com.bodhisearch.util.KeycloakAdminClient;
import com.bodhisearch.util.KeycloakTestUtils;
import com.bodhisearch.util.StaticServer;

import dasniko.testcontainers.keycloak.KeycloakContainer;
import io.restassured.response.Response;

@Testcontainers
public class BaseIntegrationTest {
  public static final String REALM = "bodhi";
  public static final String PROVIDER_ID = "bodhi";
  public static final Logger LOGGER = LoggerFactory.getLogger(BaseIntegrationTest.class);

  protected static BodhiProviderClient bodhiProviderClient;
  protected static KeycloakAdminClient keycloakAdminClient;
  protected static String keycloakBaseUrl;
  protected static StaticServer testAppServer;

  @Container
  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:23.0.7")
      .withFeaturesEnabled("admin-fine-grained-authz", "token-exchange")
      .withProviderClassesFrom("target/classes")
      .withEnv("APP_ENV", "test");

  @BeforeAll
  public static void setupKeycloakForIntegration() throws Exception {
    // Generate realm configuration specifically for integration tests
    String filename = "./src/test/resources/import-files/bodhi-realm-integration-generated.json";
    RealmConfigGenerator.generateForIntegrationTests(filename);

    // Import realm configuration
    keycloakBaseUrl = keycloak.getAuthServerUrl();
    KeycloakTestUtils.importFile(keycloakBaseUrl, keycloak.getAdminUsername(),
        keycloak.getAdminPassword(), filename);

    // Setup utility classes
    bodhiProviderClient = new BodhiProviderClient(keycloakBaseUrl, REALM, PROVIDER_ID);
    keycloakAdminClient = new KeycloakAdminClient(keycloakBaseUrl, REALM, PROVIDER_ID,
        keycloak.getKeycloakAdminClient());

    // Start test app server
    startTestAppServer();

    LOGGER.info("Keycloak integration test setup completed at: {}", keycloakBaseUrl);
  }

  @AfterAll
  public static void tearDownIntegrationTest() {
    stopTestAppServer();
    LOGGER.info("Integration test teardown completed");
  }

  protected static void startTestAppServer() {
    try {
      // Use Java static server to serve the test app
      testAppServer = new StaticServer("src/test/resources/test-app");
      int port = testAppServer.start();

      LOGGER.info("Test app server started on port {} at URL: {}", port, testAppServer.getBaseUrl());
    } catch (Exception e) {
      LOGGER.error("Failed to start test app server", e);
      throw new RuntimeException(e);
    }
  }

  protected static void stopTestAppServer() {
    if (testAppServer != null) {
      testAppServer.stop();
      LOGGER.info("Test app server stopped");
    }
  }

  protected String getTestAppUrl() {
    return testAppServer != null ? testAppServer.getBaseUrl() : null;
  }

  protected int getTestAppPort() {
    return testAppServer != null ? testAppServer.getPort() : 0;
  }

  // Delegate methods to appropriate clients
  protected ClientPair registerClientAndReturnClientPair(String redirectUri) {
    return bodhiProviderClient.registerClientAndReturnClientPair(redirectUri);
  }

  protected Response makeResourceAdmin(String token, String userToMakeFirstAdmin) {
    return bodhiProviderClient.makeResourceAdmin(token, userToMakeFirstAdmin);
  }

  protected Response addUserToGroupViaAPI(String token, String testUser, String group) {
    return bodhiProviderClient.addUserToGroup(token, testUser, group);
  }

  protected String getTokenForClient(ClientPair clientPair) {
    return keycloakAdminClient.getTokenForClient(clientPair);
  }
}