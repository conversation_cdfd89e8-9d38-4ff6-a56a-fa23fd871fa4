package com.bodhisearch;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.bodhisearch.util.ClientPair;
import com.bodhisearch.util.TokenPair;

/**
 * Integration test class for complete end-to-end workflows in the Bodhi
 * Extension.
 * 
 * This class focuses on testing full business processes and complex scenarios
 * that involve multiple components working together, such as:
 * - Complete token exchange flows
 * - End-to-end user workflows
 * - Complex permission scenarios
 * - Multi-step business processes
 */
public class BodhiIntegrationTest extends BaseTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(BodhiIntegrationTest.class);

  // ========================================
  // TOKEN EXCHANGE INTEGRATION TESTS
  // ========================================

  @Test
  public void testOfflineTokenExchangeFlow() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    TestUser adminUser = createUser();

    String clientToken = getTokenForClient(clientPair);
    makeResourceAdmin(clientToken, adminUser.email)
        .then()
        .statusCode(201);

    TokenPair userTokenPair = getUserTokenPairWith(clientPair,
        adminUser.email,
        adminUser.password,
        Arrays.asList("openid", "email", "profile", "roles"));
    DecodedJWT jwt = JWT.decode(userTokenPair.access);
    String keyId = jwt.getKeyId();

    String[] offlineTokenPair = exchangeToOfflineToken(clientPair, userTokenPair.access,
        Arrays.asList("offline_access", "scope_token_power_user"));
    String offlineAccessToken = offlineTokenPair[0];
    String offlineRefreshToken = offlineTokenPair[1];

    // Verify offline token properties
    DecodedJWT offlineJwt = JWT.decode(offlineAccessToken);
    String offlineClaimScope = offlineJwt.getClaim("scope").asString();
    List<String> offlineClaimScopes = Arrays.asList(offlineClaimScope.split(" "));
    assertEquals(new HashSet<>(Arrays.asList("offline_access", "scope_token_power_user")),
        new HashSet<>(offlineClaimScopes));
    assertTrue(offlineJwt.getClaim("resource_access").isMissing());
    assertEquals(keyId, offlineJwt.getKeyId());
    assertEquals("RS256", offlineJwt.getAlgorithm());

    // Verify refresh token flow maintains offline token properties
    TokenPair newOfflineTokens = refreshTokenFlow(clientPair, offlineRefreshToken);
    DecodedJWT newOfflineJwt = JWT.decode(newOfflineTokens.access);
    String newOfflineClaimScope = newOfflineJwt.getClaim("scope").asString();
    List<String> newOfflineClaimScopes = Arrays.asList(newOfflineClaimScope.split(" "));
    assertEquals(new HashSet<>(Arrays.asList("offline_access", "scope_token_power_user")),
        new HashSet<>(newOfflineClaimScopes));
    assertTrue(newOfflineJwt.getClaim("resource_access").isMissing());
    assertEquals(keyId, newOfflineJwt.getKeyId());
    assertEquals("RS256", newOfflineJwt.getAlgorithm());
  }
}