{
  "realm": "bodhi",
  "enabled": true,
  "registrationAllowed": true,
  "registrationEmailAsUsername": true,
  "rememberMe": true,
  "verifyEmail": false,
  "loginWithEmailAllowed": true,
  "clientScopes": [
    {
      "name": "roles",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "true",
        "gui.order": "",
        "consent.screen.text": ""
      }
    },
    {
      "name": "scope_token_user",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "false",
        "gui.order": "",
        "consent.screen.text": ""
      }
    },
    {
      "name": "scope_token_power_user",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "false",
        "gui.order": "",
        "consent.screen.text": ""
      }
    }
  ],
  "clients": [
<#if resources?? && resources?size gt 0>
<#list resources as resource>
    {
      "clientId": "${resource}",
      "enabled": true,
      "clientAuthenticatorType": "client-secret",
      "secret": "change-me",
      "redirectUris": ["http://localhost/callback"],
      "webOrigins": ["+"],
      "bearerOnly": false,
      "consentRequired": false,
      "standardFlowEnabled": true,
      "implicitFlowEnabled": false,
      "directAccessGrantsEnabled": true,
      "serviceAccountsEnabled": true,
      "publicClient": false,
      "protocol": "openid-connect",
      "fullScopeAllowed": false
    }<#sep>,</#sep></#list>,</#if>
<#if clients?? && clients?size gt 0>
<#list clients as client>
    {
      "clientId": "${client}",
      "enabled": true,
      "clientAuthenticatorType": "client-secret",
      "secret": "change-me",
      "redirectUris": ["*"],
      "webOrigins": ["*"],
      "bearerOnly": false,
      "consentRequired": false,
      "standardFlowEnabled": true,
      "implicitFlowEnabled": false,
      "directAccessGrantsEnabled": true,
      "serviceAccountsEnabled": false,
      "publicClient": true,
      "protocol": "openid-connect",
      "fullScopeAllowed": false
    }<#sep>,</#sep></#list>,</#if>
    {
      "clientId": "realm-management",
      "name": "${'${'}client_realm-management${'}'}",
      "enabled": true,
      "authorizationServicesEnabled": true,
      "authorizationSettings": {
        "allowRemoteResourceManagement": false,
        "policyEnforcementMode": "ENFORCING",
        "resources": [
          <#assign exchangeResources = (resources??)?then(resources?filter(item -> !(skipTokenExchange?? && skipTokenExchange?seq_contains(item))), [])>
          <#if exchangeResources?size gt 0>
          <#list exchangeResources as resource>
          {
            "name": "client.resource.$${resource}",
            "type": "Client"
          }<#sep>,</#sep>
          </#list>
          </#if>
        ],
        "policies": [
          {
            "name": "audience-match-policy",
            "type": "audience-match",
            "logic": "POSITIVE",
            "decisionStrategy": "UNANIMOUS",
            "config": {}
          }<#if exchangeResources?size gt 0>,</#if>
          <#if exchangeResources?size gt 0>
          <#list exchangeResources as resource>
          {
            "name": "token-exchange.permission.client.$${resource}",
            "type": "scope",
            "logic": "POSITIVE",
            "decisionStrategy": "UNANIMOUS",
            "config": {
              "resources": "[\"client.resource.$${resource}\"]",
              "scopes": "[\"token-exchange\"]",
              "applyPolicies": "[\"audience-match-policy\"]"
            }
          }<#sep>,</#sep>
          </#list>
          </#if>
        ],
        "scopes": [
          {
            "name": "token-exchange"
          }
        ],
        "decisionStrategy": "UNANIMOUS"
      }
    }
  ],
  "roles": {
    "client": {
      <#assign groupResources = (resources??)?then(resources?filter(item -> !(skipGroup?? && skipGroup?seq_contains(item))), [])>
      <#if groupResources?size gt 0>
      <#list groupResources as resource>
      "${resource}": [
        <#if clientRoles?? && clientRoles?size gt 0>
        <#list clientRoles as clientRole>
        {
          "name": "${clientRole}",
          "composite": false,
          "clientRole": true
        }<#sep>,</#sep>
        </#list>
        </#if>
      ]<#sep>,</#sep>
      </#list>
      </#if>
    }
  },
  "users": [
    <#if users?? && users?size gt 0>
    <#list users as user>
    {
      "username": "${user.email}",
      "email": "${user.email}",
      "enabled": true,
      "emailVerified": true,
      "firstName": "${user.firstName}",
      "lastName": "${user.lastName}",
      "credentials": [
        {
          "type": "password",
          "value": "pass"
        }
      ],
      "groups": [
        <#if user.groups?? && user.groups?size gt 0>
        <#list user.groups as group>
        "${group}"<#sep>,</#sep>
        </#list>
        </#if>
      ]
    }<#sep>,</#sep>
    </#list>
    </#if>
  ],
  "groups": [
    <#if groupResources?size gt 0>
    <#list groupResources as resource>
    {
      "name": "users-${resource}",
      "path": "/users-${resource}",
      "subGroups": [
        <#if userGroups?? && userGroups?size gt 0>
        <#list userGroups as userGroup>
        {
          "name": "${userGroup.name}",
          "path": "/users-${resource}/${userGroup.name}",
          "clientRoles": {
            "${resource}": [
              <#if userGroup.roles?? && userGroup.roles?size gt 0>
              <#list userGroup.roles as role>
              "${role}"<#sep>,</#sep>
              </#list>
              </#if>
            ]
          }
        }<#sep>,</#sep>
        </#list>
        </#if>
      ]
    }<#sep>,</#sep>
    </#list>
    </#if>
  ],
  "defaultDefaultClientScopes": [
  ],
  "defaultOptionalClientScopes": [
    "offline_access",
    "address",
    "phone",
    "microprofile-jwt",
    "acr",
    "email",
    "profile",
    "role_list",
    "roles",
    "web-origins",
    "scope_token_user",
    "scope_token_power_user"
  ]
}
