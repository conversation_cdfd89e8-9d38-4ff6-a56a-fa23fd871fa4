{"realm": "bodhi", "enabled": true, "registrationAllowed": true, "registrationEmailAsUsername": true, "rememberMe": true, "verifyEmail": false, "loginWithEmailAllowed": true, "clientScopes": [{"name": "roles", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}}, {"name": "scope_token_user", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false", "gui.order": "", "consent.screen.text": ""}}, {"name": "scope_token_power_user", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false", "gui.order": "", "consent.screen.text": ""}}], "clients": [{"clientId": "resource-seed", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["http://localhost/callback"], "webOrigins": ["+"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "realm-management", "name": "${client_realm-management}", "enabled": true, "authorizationServicesEnabled": true, "authorizationSettings": {"allowRemoteResourceManagement": false, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "client.resource.$resource-seed", "type": "Client"}], "policies": [{"name": "audience-match-policy", "type": "audience-match", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {}}, {"name": "token-exchange.permission.client.$resource-seed", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.$resource-seed\"]", "scopes": "[\"token-exchange\"]", "applyPolicies": "[\"audience-match-policy\"]"}}], "scopes": [{"name": "token-exchange"}], "decisionStrategy": "UNANIMOUS"}}], "roles": {"client": {"resource-seed": [{"name": "resource_user", "composite": false, "clientRole": true}, {"name": "resource_power_user", "composite": false, "clientRole": true}, {"name": "resource_manager", "composite": false, "clientRole": true}, {"name": "resource_admin", "composite": false, "clientRole": true}]}}, "users": [], "groups": [{"name": "users-resource-seed", "path": "/users-resource-seed", "subGroups": [{"name": "users", "path": "/users-resource-seed/users", "clientRoles": {"resource-seed": ["resource_user"]}}, {"name": "power-users", "path": "/users-resource-seed/power-users", "clientRoles": {"resource-seed": ["resource_user", "resource_power_user"]}}, {"name": "managers", "path": "/users-resource-seed/managers", "clientRoles": {"resource-seed": ["resource_user", "resource_power_user", "resource_manager"]}}, {"name": "admins", "path": "/users-resource-seed/admins", "clientRoles": {"resource-seed": ["resource_user", "resource_power_user", "resource_manager", "resource_admin"]}}]}], "defaultDefaultClientScopes": [], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt", "acr", "email", "profile", "role_list", "roles", "web-origins", "scope_token_user", "scope_token_power_user"]}