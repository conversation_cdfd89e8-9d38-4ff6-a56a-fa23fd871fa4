<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="OAuth test application for Keycloak integration testing">
  <meta name="keywords" content="oauth, keycloak, test, integration, authentication">
  <title>OAuth Test App</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 50px auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      background-color: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .btn {
      background-color: #007bff;
      color: white;
      padding: 12px 24px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin: 10px 0;
    }
    .btn:hover {
      background-color: #0056b3;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    .token-display {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      margin: 10px 0;
      word-break: break-all;
      font-family: 'Courier New', monospace;
      font-size: 12px;
    }
    .hidden {
      display: none;
    }
    .form-group {
      margin: 15px 0;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>OAuth Test Application</h1>
    
    <div id="config-section">
      <h2>OAuth Configuration</h2>
      <div class="form-group">
        <label for="keycloak-url">Keycloak URL:</label>
        <input type="text" id="keycloak-url" placeholder="http://localhost:8080" />
      </div>
      <div class="form-group">
        <label for="client-id">Client ID:</label>
        <input type="text" id="client-id" placeholder="your-client-id" />
      </div>
      <div class="form-group">
        <label for="client-secret">Client Secret:</label>
        <input type="text" id="client-secret" placeholder="your-client-secret" />
      </div>
      <button class="btn" onclick="startOAuthFlow()">Start OAuth Flow</button>
    </div>

    <div id="error-section" class="hidden">
      <div id="error-message" class="error"></div>
      <button class="btn" onclick="resetApp()">Reset</button>
    </div>

    <div id="success-section" class="hidden">
      <div class="success">OAuth flow completed successfully!</div>
      <h3>Access Token:</h3>
      <div id="access-token" class="token-display"></div>
      <h3>Token Claims:</h3>
      <div id="token-claims" class="token-display"></div>
      <button class="btn" onclick="resetApp()">Reset</button>
    </div>

    <div id="loading-section" class="hidden">
      <div>Processing OAuth callback...</div>
    </div>
  </div>

  <script>
    // Store OAuth configuration in sessionStorage
    let oauthConfig = {};

    // Check if we're handling a callback
    window.addEventListener('load', function() {
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      const error = urlParams.get('error');
      const state = urlParams.get('state');

      if (error) {
        showError(`OAuth error: ${error}`);
        return;
      }

      if (code) {
        // We're in callback mode
        showLoading();
        
        // Get stored config
        const storedConfig = sessionStorage.getItem('oauthConfig');
        if (!storedConfig) {
          showError('OAuth configuration not found. Please start the flow again.');
          return;
        }

        try {
          oauthConfig = JSON.parse(storedConfig);
        } catch (e) {
          showError('Invalid OAuth configuration stored. Please start the flow again.');
          return;
        }

        // Exchange code for token
        exchangeCodeForToken(code);
      } else {
        // Normal page load - show config section
        showConfigSection();
      }
    });

    function showConfigSection() {
      hideAllSections();
      document.getElementById('config-section').classList.remove('hidden');
    }

    function showError(message) {
      hideAllSections();
      document.getElementById('error-message').textContent = message;
      document.getElementById('error-section').classList.remove('hidden');
    }

    function showSuccess(accessToken, tokenClaims) {
      hideAllSections();
      document.getElementById('access-token').textContent = accessToken;
      document.getElementById('token-claims').textContent = JSON.stringify(tokenClaims, null, 2);
      document.getElementById('success-section').classList.remove('hidden');
    }

    function showLoading() {
      hideAllSections();
      document.getElementById('loading-section').classList.remove('hidden');
    }

    function hideAllSections() {
      document.getElementById('config-section').classList.add('hidden');
      document.getElementById('error-section').classList.add('hidden');
      document.getElementById('success-section').classList.add('hidden');
      document.getElementById('loading-section').classList.add('hidden');
    }

    function resetApp() {
      sessionStorage.removeItem('oauthConfig');
      // Clear URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
      showConfigSection();
    }

    function startOAuthFlow() {
      const keycloakUrl = document.getElementById('keycloak-url').value.trim();
      const clientId = document.getElementById('client-id').value.trim();
      const clientSecret = document.getElementById('client-secret').value.trim();

      if (!keycloakUrl || !clientId || !clientSecret) {
        showError('Please fill in all configuration fields');
        return;
      }

      // Store config for callback
      oauthConfig = {
        keycloakUrl: keycloakUrl,
        clientId: clientId,
        clientSecret: clientSecret
      };
      sessionStorage.setItem('oauthConfig', JSON.stringify(oauthConfig));

      // Build authorization URL
      const redirectUri = window.location.origin + window.location.pathname;
      const state = generateRandomString(16);
      const scope = 'openid email profile roles';

      const authUrl = `${keycloakUrl}/realms/bodhi/protocol/openid-connect/auth?` +
        `response_type=code&` +
        `client_id=${encodeURIComponent(clientId)}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `scope=${encodeURIComponent(scope)}&` +
        `state=${encodeURIComponent(state)}`;

      // Redirect to authorization server
      window.location.href = authUrl;
    }

    async function exchangeCodeForToken(code) {
      try {
        const redirectUri = window.location.origin + window.location.pathname;
        const tokenUrl = `${oauthConfig.keycloakUrl}/realms/bodhi/protocol/openid-connect/token`;

        const response = await fetch(tokenUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            grant_type: 'authorization_code',
            client_id: oauthConfig.clientId,
            client_secret: oauthConfig.clientSecret,
            code: code,
            redirect_uri: redirectUri
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Token exchange failed: ${response.status} ${response.statusText}\n${errorText}`);
        }

        const tokenData = await response.json();
        
        if (tokenData.error) {
          throw new Error(`Token exchange error: ${tokenData.error} - ${tokenData.error_description || ''}`);
        }

        const accessToken = tokenData.access_token;
        if (!accessToken) {
          throw new Error('No access token received from token exchange');
        }

        // Decode JWT to show claims
        const tokenClaims = parseJWT(accessToken);
        
        showSuccess(accessToken, tokenClaims);

      } catch (error) {
        console.error('Token exchange error:', error);
        showError(`Failed to exchange code for token: ${error.message}`);
      }
    }

    function parseJWT(token) {
      try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));

        return JSON.parse(jsonPayload);
      } catch (error) {
        console.error('JWT parsing error:', error);
        return { error: 'Failed to parse JWT token' };
      }
    }

    function generateRandomString(length) {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
      }
      return result;
    }
  </script>
</body>
</html> 