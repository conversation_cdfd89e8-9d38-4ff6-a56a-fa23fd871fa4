# Makefile for Keycloak Bodhi Extension

.PHONY: clean compile test test-unit test-integration help

# Default target
help:
	@echo "Available targets:"
	@echo "  clean         - Clean the project"
	@echo "  compile       - Compile the project"
	@echo "  test          - Clean, compile and run all tests"
	@echo "  test-unit     - Run only unit tests"
	@echo "  test-integration - Run only integration tests"

# Clean the project
clean:
	@echo "Cleaning project..."
	./mvnw clean

# Compile the project
compile:
	@echo "Compiling project..."
	./mvnw compile test-compile

# Full test suite (clean, compile, and run all tests)
test: clean compile
	@echo "Running all tests..."
	./mvnw test

# Run only unit tests (excluding integration tests)
test-unit: compile
	@echo "Running unit tests..."
	./mvnw surefire:test -Dtest="*Test"

# Run only integration tests
test-integration: compile
	@echo "Running integration tests..."
	./mvnw surefire:test -Dtest="*IntegrationTest"
