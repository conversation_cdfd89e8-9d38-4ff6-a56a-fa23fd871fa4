# Setting up Keycloak for Bodhi Server - Checklist

- Create a new realm `bodhi`
- In the new realm, make every client scope Optional. These include - acr, profile, role_list, roles, web-origins
- there is no need to create client global scopes, as our scope are at client level  
- Clients > realm-management > Authorization > Policies > Create Client Policy
 - Select audience match policy
 - Give name audience-match-policy
 - Save
- in Realm Settings > Login
 - Email as username
 - Login with email
 - Verify email
- Realm Settings > Email
 - Provide email template for Verify email
 - Setup SMTP provider
- Realm Setting > Session
 - Increase sso idle time to 7 days
 - Increase sso max time, sso idle remember me, sso session max remember me to 30 days
 


Analyze the Keycloak extension project at `/Users/<USER>/Documents/workspace/src/github.com/BodhiSearch/keycloak-bodhi-ext` and create comprehensive AI-focused documentation in the `ai-docs/03-context/` directory.

**Project Context:**
This is a Keycloak SPI (Service Provider Interface) extension that implements a multi-tenant OAuth2 token exchange system with client-level role-based access control. The system supports:
- Public app/frontend clients (no client secrets) that authenticate users
- OAuth resource servers (backend APIs) that require authorized access
- Client-scoped roles (admin, manager, power_user, user) managed through group membership
- These are client level roles and not global roles. So if a user is admin on a client A, he might be a power user on client B, and not have a role on client C. So these are not global roles and only client level roles.
- The role assignment is achieved through groups. Each client have a group, by convention named users-{client-id}, with four subgroups same as role names plural (admins, managers, power-users, users). Adding to these subgroups automatically provide the member with the client level role for that client.
- Token exchange from app client tokens to resource server tokens
- Audience-based authorization policies for token exchange
- The project mainly deals with token exchanges and allows token issued by app/frontend clients, these tokens are sent by the oauth resource server when forwarded to them by the app/frontend to oauth resource server, which in turn tries to exchange these app tokens to the its own OAuth resource server tokens by the auth server so that the frontend can be allowed to access  oauth resource server APIs at the level requested and available to the user.

**Key Components to Analyze:**
1. `src/main/java/com/bodhisearch/AudienceMatchPolicyProvider.java` - Token exchange authorization policy, checks if the token exchange should be allowed it allows the token exchange given the one requesting the token exchange is added to the audience and the token is issued by the same issuer
2. `src/main/java/com/bodhisearch/BodhiResourceProvider.java` - Client setup and admin assignment APIs, allows to assign an admin to a newly created client. It also has some other utility APIs like adding user to groups and checking if there is already an admin.
3. `src/test/java/com/bodhisearch/` @/Users/<USER>/Documents/workspace/src/github.com/BodhiSearch/keycloak-bodhi-ext/src/test/java/com/bodhisearch/ - Complete test suite for understanding application behavior
4. Project structure, build configuration, and deployment artifacts

**Documentation Requirements:**
Create 2-5 logically grouped files in `ai-docs/03-context/` that provide:
- split the context gathered into fewer files 2-5 that are logically grouped, and contains details about the project in it
- if required, one or multiple of these files can be provided as context to ai coding assistant helping it stay on track, get required background information specific to project, also follow the convention followed by the project 
- Once we have these files ready we should be able to attach one or more of these files to:
= Give context to AI coding assistants about the application domain and all the components in it
= The AI code is assistant should get aware of Oauth, Flows, and Token Exchange and various sequences of requests and flows that are present
= Should be aware of what all files and folders are present and what is the purpose of each of those files and folders.
= Should be aware of the conventions, especially the development process followed that is having good quality tests around the critical component as well as stress on test coverage, maintainable tests etc.
= Have AI coding assistant generate features spec that is in compliance with the domain, process and convention followed in the project.

**Target Audience:** 
Primary: AI coding assistants that need project-specific context to generate compliant code
Secondary: Human developers requiring project onboarding
- AI coding assistant already has inbuilt knowledge about many of the frameworks, libraries, patterns so we do not need to go into detail about these general public library knowledge. For e.g. keycloak is a very standard open source oauth server implementation. so we do not need to go in detail about oauth flows, or keycloak server. 
- What we need to go into detail about is, is things specific to this particular project, what it is trying to achieve, what it has achieved and how it has achieved it and if required to extend it, what are the conventions and practices that needs to be followed to add new features, test types, and location of different components/tests

**Focus Areas:**
- Project-specific implementations, NOT general Keycloak/OAuth2 concepts
- Actual code patterns, naming conventions, and architectural decisions used in THIS project
- Test organization and coverage strategies specific to this codebase
- Token exchange flow sequences and client relationship models unique to this implementation

**Deliverable:** 
Documentation that enables AI assistants to understand the project's domain model, generate feature specifications aligned with existing patterns, and produce code that follows the project's established conventions for testing, organization, and implementation.

 You need to thoroughly analyze this particular project, think deeply, and then we need to produce the docs as detailed above.